{"private": true, "scripts": {"start": "export NODE_OPTIONS='--openssl-legacy-provider --max-old-space-size=8192' && UMI_ENV=development umi dev", "build": "UMI_ENV=development umi build", "build:dev": "UMI_ENV=development umi build", "build:test": "UMI_ENV=test umi build", "build:prod": "UMI_ENV=production umi build", "build:gcp": "UMI_ENV=gcp umi build", "build:cn": "UMI_ENV=prodcn umi build", "build:eu": "UMI_ENV=prodeu umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/charts": "^1.0.0-beta.1", "@ant-design/pro-layout": "^5.0.12", "@antv/data-set": "^0.11.7", "@antv/g2plot": "^1.1.25", "@arco-design/web-react": "^2.61.1", "@arco-themes/react-easy-email-pro": "0.0.1", "@arco-themes/react-easy-email-theme": "0.0.3", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@dnd-kit/core": "^6.0.8", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@douyinfe/semi-ui": "^2.68.4", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.2", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.6", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.0", "@editorjs/link": "^2.6.2", "@editorjs/list": "^1.10.0", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.2", "@editorjs/raw": "^2.5.0", "@editorjs/simple-image": "^1.6.0", "@editorjs/underline": "^1.2.1", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@react-pdf/renderer": "2.0.0", "@studio-freight/lenis": "^1.0.42", "@twilio/voice-sdk": "^2.14.0", "@uiw/react-markdown-editor": "^3.2.9", "@uiw/react-md-editor": "^3.8.1", "@umijs/plugin-esbuild": "^1.4.2", "@umijs/preset-react": "1.x", "@umijs/test": "^3.2.17", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "amazon-chime-sdk-component-library-react": "^3.9.0", "amazon-chime-sdk-js": "^3.25.0", "amazon-connect-chatjs": "1.5.1", "amazon-connect-streams": "2.11.0", "antd": "^4.12.1", "aos": "^3.0.0-beta.6", "array-move": "^4.0.0", "axios": "^0.21.1", "create-puzzle": "^2.0.0", "crypto-js": "^4.1.1", "easy-email-pro-asset-manager": "1.22.0", "easy-email-pro-core": "1.22.0", "easy-email-pro-editor": "1.22.0", "easy-email-pro-kit": "1.22.0", "easy-email-pro-localization": "1.22.0", "easy-email-pro-plugins": "1.22.0", "easy-email-pro-theme": "1.22.0", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "editorjs-html": "^3.4.3", "editorjs-text-color-plugin": "git+https://github.com/dariox64/editorjs-text-color-plugin.git", "emoji-mart": "^5.5.2", "framer-motion": "4.1.17", "html2canvas": "1.4.1", "jsencrypt": "^3.2.1", "jwt-decode": "^3.1.2", "libphonenumber-js": "^1.11.16", "lint-staged": "^10.0.7", "liquidjs": "^10.10.2", "localStorage": "^1.0.4", "lodash": "^4.17.21", "mammoth": "^1.9.0", "mjml-browser": "^4.15.3", "moment": "^2.29.1", "pdfjs-dist": "2.12.313", "pinyin": "^3.0.0-alpha.6", "pinyin-pro": "^3.18.2", "prettier": "^1.19.1", "pubsub-js": "^1.9.3", "qrcode.react": "^4.0.1", "rc-slider-captcha": "^1.3.0", "rc-tween-one": "^3.0.3", "react": "^16.14.0", "react-avatar-editor": "^13.0.0", "react-captcha-code": "^1.0.7", "react-chrono": "^2.6.1", "react-color": "^2.19.3", "react-cookies": "^0.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^16.12.0", "react-draggable": "4.3.0", "react-final-form": "^6.5.9", "react-flags-select": "^2.2.3", "react-flow-renderer": "^10.3.17", "react-frame-component": "^5.2.6", "react-h5-audio-player": "^3.10.0", "react-helmet": "^6.1.0", "react-highlight-words": "^0.17.0", "react-horizontal-timeline": "^1.5.3", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.4", "react-intersection-observer": "8.32.0", "react-json-view": "^1.21.3", "react-markdown": "^7.1.0", "react-number-format": "^4.4.1", "react-pdf": "5.x", "react-quill": "^2.0.0", "react-scroll": "^1.9.3", "react-sortable-hoc": "^2.0.0", "react-syntax-highlighter": "^15.5.0", "react-usestateref": "^1.0.8", "rehype-raw": "6.1.1", "remark-gfm": "3.0.1", "rsuite": "^5.39.0", "slate": "^0.102.0", "slate-history": "^0.100.0", "slate-react": "^0.102.0", "styled-components": "^5.2.1", "umi": "^3.2.17", "uuid": "^8.3.2", "vditor": "^3.9.6", "weixin-js-sdk": "^1.6.5", "xlsx": "^0.18.5", "yorkie": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/preset-env": "^7.26.7", "@babel/preset-react": "^7.25.9", "@svgr/webpack": "^8.1.0", "babel-loader": "^9.2.1", "typescript": "^4.1.0"}, "name": "goclouds_crm_platform_web", "version": "1.0.0", "main": "index.js", "repository": "git@***********:goclouds_crm_platform_web", "author": "lihao <<EMAIL>>", "license": "MIT"}