import React, { Component } from 'react';
import ChatLayout from './chatLayout';
import ChatList from './chatList';
import { FormattedMessage, getIntl, Link, getLocale } from 'umi';
import { v4 as uuidv4 } from 'uuid';

import { connect as umiConnect } from 'umi';
import FunctionalPlate from './functionalPlate';
import { notification } from '../../utils/utils';
import WorkOrderDetailContent from '@/pages/ticketCenter/common/workOrderManagement/workOrderDetail';
import styles from './index.less';
import {
  IMWebClient,
  generateUUID,
  getCurrentDateTime,
  getCurrentDateTimeV2,
  getDownloadPreSignedUrl,
} from '../../pages/agentImChat/sdk/im.web.sdk';
import { handleSaveImParams } from './tools';
import AiIntelligenceSummary from './chatLayout/AiIntelligenceService/index';
// import AiIntelligenceSummaryBtn from './chatLayout/AiIntelligenceServiceBtn/index';
import { uploadParams } from './tools';
import { notifyMe } from '@/utils/utils';

class WorktableContent extends Component {
  constructor(props) {
    super(props);
    this.imContainerRef = React.createRef();
    this.audioRef = React.createRef();
    this.chatListRef = React.createRef();
    this.state = {
      isBeInputting: false,
      leftWidth: 290,
      rightWidth: 395,
      isShowModal: false,
      ticketIdList: [], //该坐席下所有在线用户的工单id，用于im建立成组
      allCustomerList: [], //该坐席下的所有客户，不受分页限制
      // isShowModalBtn: false,
      serviceSummaryModal: false, // ai智能总结弹窗
      // content: "啊的看法哪上课",
      // contentType: 'markdown',
      // fileName: '',
      // role: "customer",
      // status: 0,
      // ticketId: "6e82762a-e5f1-417b-874a-d75e4afa6translationContent:11translationStatus",
      // type: "role",
      // uid: "yau0prsn91729585874483",
      // username: 'liangliang',
      roleId: '',
      ticketId: '', // 工单id
      phoneNumberId: '', //用作判断电话只展示电话工单，不展示聊天的
      contactId: '',
      ageent: '0',
      channelConfigId: '', // 渠道id
      currentSessionData: {}, // 选中的当前会话
      sessionList: [], // 会话列表
      isIncomingCall: false, // 是否存在来电
      // 来电铃声相关
      audio: new Audio(
        `https://${process.env.DOMAIN_NAME_OVER}/static-icon/prompt_sound/connectnow_receive_message.mp3`,
      ),
      /**
       * 重新定义数据结构，全局使用。
       * [
  {
    ticketId: "", // 工单id
    ticketStatus: 0, // 工单状态 0：未分配，1：进行中，2: ACW, 3: 已结束
    ticketCode: "************", // 工单编号
    ticketLevel: "3", // 工单等级
    ticketTime: new Date(), // 工单时间
    ticketType: "1", // 工单类型
    uid: "123", // 客户id
    username: "gaosongliang", // 客户名称
    channelConfigId:'' //渠道Id
    channelId: "456", //渠道类型id
    channelType: "8", // 渠道类型
    invitationEvaluationStatus: false, // 是否发送邀请
    translateStatus: false, // 是否开启翻译
    translateCode: "", // 翻译目标Code
    unReadCount: "", // 消息未读数量
        selected:false,//选中状态
originalStatus:true//是否存在原工单id
    messageList: [
      {
        id: "8ae7e3833e834839998df3dfad8a6100",
        uid: "oye065dpl1728724962972",
        username: "zhen",
        role: "agent/customer",
        type: "role/system/remake/zongjie",
        contentType: "text/image/video/audio/link",
        content: "好 收到 / https://123.png",
        fileName: "123.png/",
        status: 1,
        time: "17:24",
        translationStatus: 0,
        translationContent: "",
        uploadProgress: 0,
      },
      {
        id: "8ae7e3833e834839998df3dfad8a6100",
        uid: "oye065dpl1728724962972",
        username: "zhen",
        role: "agent/customer",
        type: "role/system/remake/zongjie",
        contentType: "text/image/video/audio/link",
        content: "好 收到 / https://123.png",
        fileName: "123.png/",
        status: 1,
        time: "17:24",
        translationStatus: 0,
        translationContent: "",
        uploadProgress: 0,
      }
    ],
  }
]
       */
      globalList: [],
      handledPendingList: [], // 处理过的未分配数据
      robotListParent: [], //机器人数据
      pendingMessages: [], // 用来存储在queryProcessingWorkOrderList执行期间收到的消息
    };
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    if (
      this.props.phoneNumber &&
      this.props.phoneNumber !== prevProps.phoneNumber
    ) {
      // 聊天框内点击当前电话，子调父调子，二三栏调用一栏拨电话功能，无其他功能
      this.handleClickCurrentPhone(this.props.phoneNumber);
    }
    // 切换待处理/未分配。刷新接口，重置当前会话数据
    //workTableTabProcesOrPendValue:工作台工单领取：'1'待处理，'2':未分配
    if (
      this.props.workTableTabProcesOrPendValue &&
      prevProps.workTableTabProcesOrPendValue !==
        this.props.workTableTabProcesOrPendValue
    ) {
      //当前会话数据置空
      this.setState({
        currentSessionData: {},
      });
      // 添加定时器,每秒轮询倒计时queryProcessingWorkOrDerListSchedule
      if (+this.props.workTableTabProcesOrPendValue === 1) {
        this.timer = setInterval(() => {
          if (this.state.globalList?.length > 0) {
            this.queryProcessingWorkOrDerListSchedule(
              {
                workOrderId: '',
                workRecordTypeCode: '',
                priorityLevelId: '',
              },
              this.state.globalList,
            );
          }
        }, 60000);
      } else {
        // 清除定时器
        if (this.timer) {
          clearInterval(this.timer);
        }
      }
    }
    // 监听切换tab时清空
    if (
      this.props.workTableTabValue &&
      prevProps.workTableTabValue !== this.props.workTableTabValue
    ) {
      this.setState({
        currentSessionData: {},
      });
    }
    //上下线调用IMWebClient.changeStatus，这里只控制im调用，其他逻辑在layout里
    if (
      this.props.agentStatus !== prevProps.agentStatus ||
      this.props.websocketStatus !== prevProps.websocketStatus
    ) {
      if (
        this.props.websocketStatus !== true ||
        this.props.agentStatus?.split('_')?.[0] !== '1'
      ) {
        //这种情况离线 statusType 固定是 0就好了 by liuyinghao！
        IMWebClient.changeStatus({
          tenantId: this.props.user.companyId,
          teamId: this.props.user.deptId,
          uid: this.props.user.userId,
          statusType: '0',
          statusName: this.props.agentStatusName,
          // statusName: getIntl().formatMessage({
          //   id: 'im.agent.status.offline',
          //   defaultMessage: 'Offline',
          // }),
          role:
            this.props.user.roleList?.[0]?.roleId === '1005'
              ? 'agent_admin'
              : 'agent',
        });
      } else if (
        this.props.websocketStatus === true &&
        this.props.agentStatus?.split('_')?.[0] === '1'
      ) {
        IMWebClient.changeStatus({
          tenantId: this.props.user.companyId,
          teamId: this.props.user.deptId,
          uid: this.props.user.userId,
          statusType: '1',
          statusName: this.props.agentStatusName,

          // statusName: getIntl().formatMessage({
          //   id: 'im.agent.status.available',
          //   defaultMessage: 'Available',
          // }),
          role:
            this.props.user.roleList?.[0]?.roleId === '1005'
              ? 'agent_admin'
              : 'agent',
        });
      }
    }
    //处理全局翻译状态
    console.log(
      this.props.workTableSettingTranslation,
      this.props.workTableSettingTranslationCode,
      prevProps.workTableSettingTranslation,
      this.state.globalList,
      JSON.parse(localStorage.getItem('globalList')),
      'this.props.workTableSettingTranslation',
    );
    if (
      this.props.workTableSettingTranslation !==
      prevProps.workTableSettingTranslation
    ) {
      // 更新全局会话列表中的翻译状态
      let newGlobalList = JSON.parse(localStorage.getItem('globalList'));
      if (newGlobalList && newGlobalList.length > 0) {
        const updatedGlobalList = newGlobalList.map(item => ({
          ...item,
          translateStatus: this.props.workTableSettingTranslation,
          translateCode: this.props.workTableSettingTranslationCode,
        }));
        localStorage.setItem('globalList', JSON.stringify(updatedGlobalList));
        if (this.state.globalList && this.state.globalList.length > 0) {
          this.UpdateLatestData(
            this.state.globalList,
            JSON.parse(localStorage.getItem('globalList')),
            'ticketId',
            'translateStatus',
            'translateCode',
            'customerSourceLanguageCode',
          );
        }
      }
    }
    if (
      this.props.workTableSettingTranslationCode !==
      prevProps.workTableSettingTranslationCode
    ) {
      // 更新全局会话列表中的翻译状态
      let newGlobalList = JSON.parse(localStorage.getItem('globalList'));
      if (newGlobalList && newGlobalList.length > 0) {
        const updatedGlobalList = newGlobalList.map(item => ({
          ...item,
          translateStatus: this.props.workTableSettingTranslation,
          translateCode: this.props.workTableSettingTranslationCode,
        }));
        localStorage.setItem('globalList', JSON.stringify(updatedGlobalList));
        if (this.state.globalList && this.state.globalList.length > 0) {
          this.UpdateLatestData(
            this.state.globalList,
            JSON.parse(localStorage.getItem('globalList')),
            'ticketId',
            'translateStatus',
            'translateCode',
            'customerSourceLanguageCode',
          );
        }
      }
    }
  }

  componentDidMount() {
    //获取所有在线用户，用于成组
    // if (this.props.user.roleList?.[0]?.roleId === '1005') {
    //   this.queryProcessingWorkOrderListAdmin();
    // } else {
    this.queryProcessingWorkOrderListAdmin();
    // }
    this.props.dispatch({
      type: 'worktable/saveWorkTableSettingTranslation',
      payload: localStorage.getItem('worktable_setting_translation') === 'true',
    });
    this.props.dispatch({
      type: 'worktable/saveWorkTableSettingTranslationCode',
      payload: localStorage.getItem('worktable_setting_translation_code'),
    });
    // 添加定时器,每秒轮询
    this.timer = setInterval(() => {
      if (this.state.globalList?.length > 0) {
        this.queryProcessingWorkOrDerListSchedule(
          {
            workOrderId: '',
            workRecordTypeCode: '',
            priorityLevelId: '',
          },
          this.state.globalList,
        );
      }
    }, 60000);
  }

  componentWillUnmount() {
    localStorage.setItem('currentSessionData', JSON.stringify({}));
    // localStorage.setItem('globalList', null);

    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  //查询处理中的客户
  queryProcessingWorkOrderListAdmin = () => {
    this.props.dispatch({
      type: 'worktable/queryProcessingWorkOrderList',
      payload: {
        page: {
          pageSize: 10000,
          pageNum: 1,
        },
        data: {
          workOrderId: '',
        },
      },
      callback: response => {
        if (response.code == 200) {
          let row = response.data.rows;
          let ticketIdList = row
            ?.map(r => {
              if ([1, 2].includes(r.status)) {
                return r.workRecordId;
              }
            })
            .filter(r => r);
          this.setState(
            {
              ticketIdList,
              allCustomerList: row,
            },
            () => {
              this.loadIMChatChannel();
            },
          );
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  loadIMChatChannel = () => {
    //记录ws连接
    this.props.dispatch({
      type: 'layouts/setWebsocketStatusNewAw',
      payload: false,
    });
    // 重置socket，设置Socket事件函数并开启新的Socket
    IMWebClient.reset();
    IMWebClient.on('open', this.handleIMChatConnectFinished);
    IMWebClient.on('message', this.handleIMChatIncomingMessage);
    IMWebClient.on('close', this.handleIMChatConnectClosed);
    IMWebClient.on('error', this.handleIMChatConnectError);
    IMWebClient.connect();
  };
  /**
   * Socket连接开启后事件，onOpen
   */
  handleIMChatConnectFinished = () => {
    // 执行SOCKET连接绑定用户
    if (this.props.user.userId) {
      IMWebClient.bindAccount({
        uid: this.props.user.userId,
        username: this.props.user.userName,
        tenantId: this.props.user.companyId,
        sessionId:
          this.state.currentSessionData?.ticketId &&
          this.state.currentSessionData?.sessionStatus &&
          this.state.currentSessionData?.sessionStatus != 3
            ? this.state.currentSessionData.ticketId
            : '',
        teamId: this.props.user.deptId,
        teamRole: this.props.user.roleList?.[0]?.roleName,
        maxServiceCount: this.props.user.maxActiveChatNum + '',
        receiveType: this.props.user.receiveTicketType,
        activeChannel: this.props.user.agentAccessChannel,
        role:
          this.props.user.roleList?.[0]?.roleId === '1005'
            ? 'agent_admin'
            : 'agent',
        ticketIdList:
          this.props.user.roleList?.[0]?.roleId === '1005'
            ? this.state.ticketIdList.join(',')
            : '',
        onlineAllowCount: this.props.user.onlineUserLimit,
      });
    }
  };

  /**
   * Socket接收到消息事件
   * @param data 消息数据
   * onMessage
   */
  handleIMChatIncomingMessage = async data => {
    console.log(
      'IMChat 收到socket消息',
      data,
      sessionStorage.getItem('currentMessageId'),
      // 标记是否正在处理来电事件
      sessionStorage.getItem('isProcessingCall'),
    );
    //拿到当前信息id，用于对后端replayMessage接口返回的消息判重
    let currentMessageId = sessionStorage.getItem('currentMessageId');
    //公共方法，回调
    const handleEvent = (key, handler) => {
      if (data.key === key) {
        handler(data);
      }
    };
    //事件类型，
    if (data.type === 'event') {
      handleEvent(
        IMWebClient.config.KEY_CLIENT_BIND,
        this.handleImConnectBindEvent,
      ); // 绑定用户事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_CHANGE_STATUS,
        this.handleImChangeStatusEvent,
      ); // 切换用户状态事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_CALL,
        this.handleImIncomingCallEvent,
      ); // 来电事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_MESSAGE_READ,
        this.handleImMessageReadEvent,
      ); // 消息已读事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_CLOSED,
        this.handleImConnectCloseEvent,
      ); // 连接断开事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_CLOSED_ACW,
        this.handleImCloseAcwEvent,
      ); // 关闭ACW事件
      handleEvent(
        IMWebClient.config.KEY_CUSTOMER_EVALUATION,
        this.handleImCustomerEvaluationEvent,
      ); // 满意度评价
      handleEvent(IMWebClient.config.KEY_CLIENT_REASSIGN, this.handleReassign); // 关闭转派事件
      handleEvent(
        IMWebClient.config.KEY_CLIENT_AGNET_STATUS,
        this.handleGetAgentStatus,
      ); // 同步在线状态事件
      handleEvent(
        IMWebClient.config.KEY_MESSAGE_WITHDRAW,
        this.handleMessageWithdrawEvent,
      ); // 消息撤回事件
      handleEvent(IMWebClient.config.KEY_BE_INPUTTING, this.handleBeInputting); // 正在输入事件
    }
    //消息类型
    if (data.type === 'message') {
      // 如果正在处理来电事件，则将消息暂存
      if (sessionStorage.getItem('isProcessingCall') === 'true') {
        this.setState(prevState => ({
          pendingMessages: [...prevState.pendingMessages, data],
        }));
        return; // 暂时不处理这条消息
      }
      //做判重避免replyMessage重复推消息
      if (data?.id && data?.id !== currentMessageId) {
        sessionStorage.setItem('currentMessageId', data.id);

        //拿到当前消息的数据对象
        const findOne = this.state.globalList?.find(
          session => session.ticketId === data.sid,
        );
        console.log('findOne', findOne);
        // 创建消息数据
        let messageData = {
          ticketId: data.sid, // 工单id
          contactId: data.extra ? JSON.parse(data.extra)?.contactId : '',
          id: data.id, // 消息id
          type: data.sender === 'system' ? 'system' : 'role', // 消息类型，role：用户消息, system：系统消息
          role: data.role, // 角色，system：系统，customer：客户，agent：座席,agent_admin:坐席管理员，aiAgent： 进线意图
          username: data.username, // 用户名称
          contentType: data.format || 'text', // 消息内容类型，text，link，image，video，audio
          content: data.content, // 消息内容，text时是文字，link、image、video时都是链接
          fileName: data.fileName, // 文件名称，用于contentType为link时显示
          filePath: data.filePath, // 文件url
          status: 0, // 消息状态：0未读，1已读
          translationContent: '', // 翻译的内容
          translationStatus: findOne?.translateStatus ? 1 : 0, // 0不翻译，1翻译中，2翻译成功，3翻译失败
          uid: data.sender, // 客户id，用于判断给哪个session中push消息
        };
        // 如果是附件类型
        if (data.extra) {
          if (['image', 'video', 'link', 'audio'].includes(data.format)) {
            const extra = JSON.parse(data.extra);
            messageData.fileName = extra.attachmentName;
          }
        }
        // 增加未读数量
        const updateReadData = this.state.globalList.map(item => {
          if (item.ticketId === data.sid && !item.selected) {
            item.unReadCount = +item?.unReadCount + 1;
          }
          return item;
        });
        console.log(updateReadData, '测离线消息updateReadData');
        this.setState(
          {
            globalList: updateReadData,
          },
          () => {
            //缓存到localStorage，公共方法，参数为key名，值
            this.cacheSessionList('globalList', this.state.globalList);
          },
        );
        // 判断如果是文本并且开启了实时翻译功能
        if (
          !['image', 'video', 'link', 'audio'].includes(data.format) &&
          findOne &&
          findOne.translateStatus &&
          findOne.translateCode &&
          !['aiAgent', 'system'].includes(data.role)
        ) {
          //这条数据的id
          await this.handleAddMessageNewIm(messageData, findOne);
        } else {
          //这里处理文本数据，没有开启翻译的数据,没有附件类型的数据
          await this.handleAddMessageNew(messageData);
        }
        //处理data.content
        let playAudio = '';
        if (data.role === 'aiAgent' && data.content) {
          playAudio = JSON.parse(data.content)?.inbound_intent;
        } else if (['textimage', 'image', 2].includes(data.format)) {
          playAudio =
            getIntl().formatMessage({
              id: 'worktable.notification.chat.content',
            }) +
            '[' +
            getIntl().formatMessage({
              id: 'knowledge.QA.label.2',
              default: '图片',
            }) +
            ']';
        } else if (['video', 'link', 'music', 3, 4, 5].includes(data.format)) {
          playAudio =
            getIntl().formatMessage({
              id: 'worktable.notification.chat.content',
            }) +
            '[' +
            data.content +
            ']';
        } else {
          playAudio =
            getIntl().formatMessage({
              id: 'worktable.notification.chat.content',
            }) + data.content;
        }
        //来消息提示音
        this.playAudio(playAudio);
      }
    }
  };
  /**
   * Socket连接关闭
   */
  handleIMChatConnectClosed = () => {
    console.log('ws连接关闭==========');
    //记录ws连接
    this.props.dispatch({
      type: 'layouts/setWebsocketStatusNewAw',
      payload: false,
    });
  };
  /**
   * Socket连接失败后事件（1，3，15s自动重连，后续手动重连）
   */
  handleIMChatConnectError = () => {
    console.log('IMChat 重新连接');
    //记录ws连接
    this.props.dispatch({
      type: 'layouts/setWebsocketStatusNewAw',
      payload: false,
    });
  };
  /**
   * 播放来电铃声
   */
  playAudio = value => {
    const { audio } = this.state;
    // 重置音频
    audio.currentTime = 0;
    audio.play();
    // 下面的逻辑都是出发浏览器级别的提示
    let title = getIntl().formatMessage({
      id: 'worktable.notification.chat.title',
    });
    let options = {
      dir: 'auto', // 文字方向
      body: value, // 通知主体
      requireInteraction: false, // 不自动关闭通知
      // 通知图标
      icon:
        'https://connectnow-demo-html-prod.s3.ap-southeast-1.amazonaws.com/ConnectNow.ico',
    };
    let notify = notifyMe(title, options);
    if (notify) {
      notify.onclick = () => {
        window.focus();
        // 调出工作台
        this.props.dispatch({
          type: 'layouts/workerTablePush',
          payload: true,
        });
        // sessionStorage.setItem('showWorkTable', true);
        //切换到聊天tab
        this.chatListRef.current?.handleChangeChat();
        notify.close();
        console.log('点击了提示');
      };
    }
  };
  /**
   * 关闭转派事件
   * */
  handleReassign = async data => {
    let sessionId = data.sessionId;
    if (sessionId != this.state.currentSessionData.ticketId) {
      //转派工单窗口置为false
      await this.props.dispatch({
        type: 'worktable/setUpgradeModalOpen',
        payload: false,
      });
      //从座席列表将原工单剔除
      this.deleteTicket(this.state.currentSessionData.ticketId);
    }
  };
  deleteTicket = id => {
    console.log('------已解决----');
    this.setState(
      prevState => {
        let updatedSessionList = prevState.globalList;
        //处理新加入的工单，工单id在原列表中存在
        let sessionIndex = prevState.globalList?.findIndex(
          f => f.ticketId === id,
        );

        if (sessionIndex !== -1) {
          updatedSessionList.splice(sessionIndex, 1);
        }
        return {
          globalList: updatedSessionList,
          ticketId: '',
          channelConfigId: '',
          currentSessionData: {},
        };
      },
      () => {
        this.cacheSessionList('globalList', this.state.globalList);
        localStorage.setItem('currentSessionData', JSON.stringify({}));
      },
    );
  };
  // 接收翻译消息
  handleAddMessageNewIm = async (data, findOne) => {
    console.log('handleAddMessageNewIm', data, findOne);
    //前端生成uuid
    let dataId = uuidv4();
    if (!data.content && !data.uid) {
      return;
    }
    let findFlag = false; //用于判断在当前列表页有没有找到对应的工单，没有的话需要在全部列表查找，并且需要置顶最新消息工单
    let updatedSessionList = [...this.state.globalList];
    let sessionTemp = null;
    let num = 0;
    //生成新的翻译后的数据覆盖旧数据
    updatedSessionList.forEach(session => {
      if (session.ticketId === data.ticketId) {
        findFlag = true;
        if (!session.messageList) {
          session.messageList = [];
        }
        if (data.id) {
          dataId = data.id;
          session.messageList = session.messageList.map(message =>
            message.id === data.id ? { ...message, ...data } : message,
          );
        }
        const newMessage = {
          ticketId: data.ticketId,
          id: dataId, // id
          time: getCurrentDateTime(), // 时间
          contactId: data.contactId,
          type: data.role === 'aiAgent' ? 'aiAgent' : data.type, // 消息类型，role：用户消息, system：系统消息
          role: data.role === 'aiAgent' ? 'system' : data.role, // 角色，system：系统，customer：客户，agent：座席,agent_admin:坐席管理员，aiAgent:意图
          uid: data.uid, // 用户id
          username: data.username, // 用户名称
          contentType: data.contentType, // 消息内容类型，text，link，image，video
          content: data.content, // 消息内容，text时是文字，link、image、video时都是链接
          fileName: data.fileName || '', // 文件名称，用于contentType为link时显示
          filePath: data.filePath || '', // 文件url

          status: 0, // 消息状态：0未读，1已读
          translationContent: data.translationContent, // 翻译的内容
          translationStatus: data.translationStatus, // 翻译的内容
          uploadProgress: 0, // 附件进度数值
          uploadPreSignedUrlResult: data.uploadPreSignedUrlResult,
          workRecordFileList: data.workRecordFileList || [],
          closeStatus: true, //用于进线意图是否展开
          referenceContentId: data.referenceContentId,
          referenceReplyPerson: data.referenceReplyPerson,
          referenceContent: data.referenceContent,
          referenceContentType: data.referenceContentType,
          referenceContentFile: data.referenceContentFile,
          //是否撤回
          dataStatus: data.data_status,
        };
        session.messageList.push(newMessage);
        sessionTemp = { ...session };
      }
      if (session?.isTop === '2') {
        num++;
      }
    });
    //这里处理当前会话列表找不到的用户，在全部列表查找
    if (!findFlag) {
      this.state.allCustomerList?.forEach(custome => {
        if (custome.ticketId === data.ticketId) {
          findFlag = true;
          if (!custome.messageList) {
            custome.messageList = [];
          }
          if (data.id) {
            dataId = data.id;
            custome.messageList = custome.messageList.map(message =>
              message.id === data.id ? { ...message, ...data } : message,
            );
          }
          const newMessage = {
            ticketId: data.ticketId,
            id: dataId, // id
            contactId: data.contactId,
            time: getCurrentDateTime(), // 时间
            type: data.role === 'aiAgent' ? 'aiAgent' : data.type, // 消息类型，role：用户消息, system：系统消息
            role: data.role === 'aiAgent' ? 'system' : data.role, // 角色，system：系统，customer：客户，agent：座席,agent_admin:坐席管理员，aiAgent:意图
            uid: data.uid, // 用户id
            username: data.username, // 用户名称
            contentType: data.contentType, // 消息内容类型，text，link，image，video
            content: data.content, // 消息内容，text时是文字，link、image、video时都是链接
            fileName: data.fileName || '', // 文件名称，用于contentType为link时显示
            filePath: data.filePath || '', // 文件url

            status: 0, // 消息状态：0未读，1已读
            translationContent: data.translationContent, // 翻译的内容
            translationStatus: data.translationStatus, // 翻译的内容
            uploadProgress: 0, // 附件进度数值
            uploadPreSignedUrlResult: data.uploadPreSignedUrlResult,
            workRecordFileList: data.workRecordFileList || [],
            closeStatus: true, //用于进线意图是否展开
            referenceContentId: data.referenceContentId,
            referenceReplyPerson: data.referenceReplyPerson,
            referenceContent: data.referenceContent,
            referenceContentType: data.referenceContentType,
            referenceContentFile: data.referenceContentFile,

            //是否撤回
            dataStatus: data.data_status,
          };
          // if(data.type === 'role' && data.role === 'agent'){
          //   newMessage.status = 1
          // }

          custome.messageList.push(newMessage);
          sessionTemp = { ...custome };
        }
        if (custome?.isTop === '2') {
          num++;
        }
      });
    } else {
      //这里如果是在已有列表匹配到，需要过滤掉
      updatedSessionList = updatedSessionList.filter(
        session => session.ticketId !== data.ticketId,
      );
    }
    // 将匹配到的session置顶
    // 将匹配到的session置顶到置顶会话后
    if (sessionTemp && sessionTemp?.isTop === '2') {
      updatedSessionList.unshift(sessionTemp);
    } else if (
      sessionTemp &&
      (sessionTemp?.isTop === '1' || !sessionTemp?.isTop)
    ) {
      updatedSessionList.splice(num, 0, sessionTemp);
    }
    // 更新会话列表数据
    this.setState({ globalList: updatedSessionList }, async () => {
      //更新数据，此处需要更新的数据已经更新完毕
      let translateResponse = this.handleGetTranslateText(
        data.ticketId,
        dataId,
        data.content,
        findOne.translateCode,
        'customer',
        updatedSessionList,
      );
      // 由于handleGetTranslateText返回的是一个Promise，需要使用await获取结果
      let translateResponseNew = await translateResponse;
      console.log(translateResponseNew, '翻译结果');
      let saveImData = {
        ticketId: data.ticketId,
        contactId: data.contactId,
        role: data.role,
        id: data.id,
        type: 'role',
        username: data.username,
        uid: data.uid,
        contentType: data.contentType,
        fileName: '',
        content: data.content,
        translationContent: translateResponseNew.data?.translatedText,
        translateLanguage: translateResponseNew.data?.sourceLanguageCode,
        referenceContentId: data.referenceContentId,
        referenceReplyPerson: data.referenceReplyPerson,
        referenceContent: data.referenceContent,
        referenceContentType: data.referenceContentType,
        referenceContentFile: data.referenceContentFile,
        //是否撤回
        dataStatus: data.data_status,
      };
      // 保存im发过来的数据，不在前端起作用
      this.handleSaveImData(saveImData);
    });
    return dataId;
  };
  // 添加消息
  handleAddMessageNew = async (data, isReplyMessage = true) => {
    console.log('handleAddMessageNew', data);
    let dataId = uuidv4();
    if (!data.content && !data.uid) {
      return;
    }
    this.setState(
      prevState => {
        let findFlag = false; //用于判断在当前列表页有没有找到对应的工单，没有的话需要在全部列表查找，并且需要置顶最新消息工单
        let updatedSessionList = [...prevState.globalList];
        let sessionTemp = null;
        let num = 0;

        // 添加到消息列表
        updatedSessionList?.forEach(session => {
          if (session.ticketId === data.ticketId) {
            findFlag = true;
            if (!session.messageList) {
              session.messageList = [];
            }
            if (data.id) {
              dataId = data.id;
              session.messageList = session.messageList.map(message =>
                message.id === data.id ? { ...message, ...data } : message,
              );
            }
            const newMessage = {
              ticketId: data.ticketId,
              id: dataId, // id
              contactId: data.contactId,
              time: getCurrentDateTime(), // 时间
              type: data.role === 'aiAgent' ? 'aiAgent' : data.type, // 消息类型，role：用户消息, system：系统消息
              role: data.role === 'aiAgent' ? 'system' : data.role, // 角色，system：系统，customer：客户，agent：座席,agent_admin:坐席管理员，aiAgent:意图
              uid: data.uid, // 用户id
              username: data.username, // 用户名称
              contentType: data.contentType, // 消息内容类型，text，link，image，video,evaluation
              content: data.content, // 消息内容，text时是文字，link、image、video时都是链接
              fileName: data.fileName || '', // 文件名称，用于contentType为link时显示
              filePath: data.filePath || '', // 文件url
              status: 0, // 消息状态：0未读，1已读
              translationContent: data.translationContent, // 翻译的内容
              translationStatus: data.translationStatus, // 翻译的内容
              uploadProgress: 0, // 附件进度数值
              uploadStatus: data.uploadStatus,
              uploadPreSignedUrlResult: data.uploadPreSignedUrlResult,
              workRecordFileList: data.workRecordFileList || [],
              closeStatus: true, //用于进线意图是否展开
              referenceContentId: data.referenceContentId,
              referenceReplyPerson: data.referenceReplyPerson,
              referenceContent: data.referenceContent,
              referenceContentType: data.referenceContentType,
              referenceContentFile: data.referenceContentFile,

              //是否撤回
              dataStatus: data.data_status,
            };
            // if(data.type === 'role' && data.role === 'agent'){
            //   newMessage.status = 1
            // }

            session.messageList.push(newMessage);
            sessionTemp = { ...session };
          }
          if (session?.isTop === '2') {
            num++;
          }
        });
        //这里处理当前会话列表找不到的用户，在全部列表查找
        if (!findFlag) {
          this.state.allCustomerList?.forEach(custome => {
            if (custome.ticketId === data.ticketId) {
              findFlag = true;
              if (!custome.messageList) {
                custome.messageList = [];
              }
              if (data.id) {
                dataId = data.id;
                custome.messageList = custome.messageList.map(message =>
                  message.id === data.id ? { ...message, ...data } : message,
                );
              }
              const newMessage = {
                ticketId: data.ticketId,
                id: dataId, // id
                contactId: data.contactId,
                time: getCurrentDateTime(), // 时间
                type: data.role === 'aiAgent' ? 'aiAgent' : data.type, // 消息类型，role：用户消息, system：系统消息
                role: data.role === 'aiAgent' ? 'system' : data.role, // 角色，system：系统，customer：客户，agent：座席,agent_admin:坐席管理员，aiAgent:意图
                uid: data.uid, // 用户id
                username: data.username, // 用户名称
                contentType: data.contentType, // 消息内容类型，text，link，image，video
                content: data.content, // 消息内容，text时是文字，link、image、video时都是链接
                fileName: data.fileName || '', // 文件名称，用于contentType为link时显示
                filePath: data.filePath || '', // 文件url

                status: 0, // 消息状态：0未读，1已读
                translationContent: data.translationContent, // 翻译的内容
                translationStatus: data.translationStatus, // 翻译的内容
                uploadProgress: 0, // 附件进度数值
                uploadPreSignedUrlResult: data.uploadPreSignedUrlResult,
                workRecordFileList: data.workRecordFileList || [],
                closeStatus: true, //用于进线意图是否展开
                referenceContentId: data.referenceContentId,
                referenceReplyPerson: data.referenceReplyPerson,
                referenceContent: data.referenceContent,
                referenceContentType: data.referenceContentType,
                referenceContentFile: data.referenceContentFile,

                //是否撤回
                dataStatus: data.data_status,
              };
              // if(data.type === 'role' && data.role === 'agent'){
              //   newMessage.status = 1
              // }

              custome.messageList.push(newMessage);
              sessionTemp = { ...custome };
            }
            if (custome?.isTop === '2') {
              num++;
            }
          });
        } else {
          //这里如果是在已有列表匹配到，需要过滤掉
          updatedSessionList = updatedSessionList.filter(
            session => session.ticketId !== data.ticketId,
          );
        }
        // 将匹配到的session置顶到置顶会话后
        if (sessionTemp && sessionTemp?.isTop === '2') {
          updatedSessionList.unshift(sessionTemp);
        } else if (
          sessionTemp &&
          (sessionTemp?.isTop === '1' || !sessionTemp?.isTop)
        ) {
          updatedSessionList.splice(num, 0, sessionTemp);
        }

        //处理附件类型
        if (
          ['image', 'video', 'link', 'audio'].includes(data.contentType) &&
          data.role === 'customer' &&
          data.type === 'role'
        ) {
          data.workRecordFileList = [];
          if (data.contentType === 'link') {
            const result = uploadParams(data.content);
            const fileObj = {
              fileName: data.fileName,
              fileUrl: data.content,
              bucketName: result.bucketName,
            };
            data.content = data.fileName;
            data.workRecordFileList.push(fileObj);
          }
          console.log('查contentID', data);
          this.handleSaveImData(data);
        }
        // 处理文本类型，不需要翻译的状况
        if (
          !['image', 'video', 'link', 'audio'].includes(data.contentType) &&
          data.translationStatus !== 1
        ) {
          //不包含备注和ai总结,这里isReplyMessage用于做三方时坐席管理员不需要重复调用replyMessage
          if (
            !['aisummary', 'remark', 'intelligentFormFilling'].includes(
              data.type,
            ) &&
            isReplyMessage
          ) {
            // 保存im发过来的数据，不在前端起作用
            let newData = {
              ...data,
              type: data.role === 'aiAgent' ? 'aiAgent' : data.type, // 消息类型，role：用户消息, system：系统消息
              role: data.role === 'aiAgent' ? 'system' : data.role, // 角色，
            };
            this.handleSaveImData(newData);
          }
        }
        console.log(updatedSessionList, '测离线消息updatedSessionList');
        return { globalList: updatedSessionList };
      },
      () => {
        //缓存到localStorage，公共方法，参数为key名，值
        this.cacheSessionList('globalList', this.state.globalList);
      },
    );
    return dataId;
  };
  //更新备注消息
  handleAddNoteNew = async data => {
    let newcurrentSessionData = this.state.currentSessionData;
    newcurrentSessionData.messageList.push(data);
    this.setState({
      currentSessionData: newcurrentSessionData,
    });
  };
  /**
   * 置顶会话
   * @param data
   */
  handleSetTopTicket = data => {
    if (!data || data.length == 0) {
      return;
    }
    this.setState(prevState => {
      let updatedSessionList = [...prevState.globalList];
      data.forEach(dItem => {
        let sessionTemp = null;

        // 添加到消息列表
        updatedSessionList?.forEach(session => {
          if (session.ticketId === dItem) {
            // const newMessage = {
            //  ...session
            // };
            session.isTop = '2';
            sessionTemp = { ...session };
          }
        });
        //这里如果是在已有列表匹配到，需要过滤掉
        updatedSessionList = updatedSessionList.filter(
          session => session.ticketId !== dItem,
        );
        // 将匹配到的session置顶
        updatedSessionList.unshift(sessionTemp);
      });
      return { globalList: updatedSessionList };
    });
  };
  /**
   * 取消置顶会话
   * @param data
   */
  handleSetNoTopTicket = data => {
    if (!data || data.length == 0) {
      return;
    }
    this.setState(prevState => {
      let updatedSessionList = [...prevState.globalList];
      data.forEach(dItem => {
        let sessionTemp = null;
        //记录置顶的会话数量
        let num = 0;
        // 添加到消息列表
        updatedSessionList?.forEach(session => {
          if (session.ticketId === dItem) {
            // const newMessage = {
            //  ...session
            // };
            session.isTop = '1';
            sessionTemp = { ...session };
          }
          if (session?.isTop === '2') {
            num++;
          }
        });

        //这里如果是在已有列表匹配到，需要过滤掉
        updatedSessionList = updatedSessionList.filter(
          session => session.ticketId !== dItem,
        );
        // 如果是取消置顶，将会话插入到所有置顶会话的后面
        if (sessionTemp && sessionTemp?.isTop === '1') {
          updatedSessionList.splice(num, 0, sessionTemp);
        }
      });
      return { globalList: updatedSessionList };
    });
  };
  // 翻译方法
  /**
   * 获取翻译内容
   * @param customerId 用户id
   * @param messageId 消息id
   * @param content 文本内容
   * @param code 目标语言code
   * @param role 角色
   * @returns {Promise<void>}
   */
  handleGetTranslateText = async (
    ticketId,
    messageId,
    content,
    code,
    role,
    updatedSessionListGlobal,
  ) => {
    let translationStatus = 1; // 0不翻译，1翻译中，2翻译成功，3翻译失败
    let translationAfterContent = '';
    let translateResponse = null;
    let globalList = updatedSessionListGlobal
      ? updatedSessionListGlobal
      : this.state.globalList;
    try {
      await this.props.dispatch({
        type: 'worktable/translate',
        payload: {
          sourceLanguageCode: '',
          textToTranslate: content,
          targetLanguageCode: code,
        },
        callback: async response => {
          translateResponse = response;
          if (response.code === 200) {
            translationStatus = 2;
            translationAfterContent = response.data.translatedText;
            if (role !== 'agent') {
              //拿到该客户的所有对象数据
              let sessionListForCustomer = globalList?.find(
                item => item.ticketId === ticketId,
              );
              console.log(
                sessionListForCustomer,
                content,
                messageId,
                '翻译=====',
              );
              if (sessionListForCustomer) {
                //更新该条数据customerSourceLanguageCode的值
                this.updateSessionBySessionId(
                  sessionListForCustomer.ticketId,
                  'customerSourceLanguageCode',
                  response.data.sourceLanguageCode,
                );
              }
            }
          } else {
            translationStatus = 3;
          }
        },
      });
    } catch (e) {
      translationStatus = 3;
    } finally {
      //代表请求超时
      if (!translateResponse && translationStatus === 1) {
        translationStatus = 3;
      }
      // 更新指定会话的指定消息的翻译内容
      const updatedSessionList = this.state.globalList.map(session => {
        if (session.ticketId === ticketId) {
          console.log(
            translationAfterContent,
            translationStatus,
            session.messageList,
            '翻译结果=====',
          );
          session.messageList = session.messageList.map(message => {
            console.log(messageId, message.id, '翻译结果messageId');
            if (message.id === messageId) {
              return {
                ...message,
                translationContent: translationAfterContent,
                translationStatus: translationStatus,
              };
            } else {
              return message;
            }
          });
        }
        return session;
      });
      console.log(updatedSessionList, translateResponse, '翻译结果最终=====');
      // 更新数据
      this.setState({
        globalList: updatedSessionList,
      });
    }
    return translateResponse;
  };

  /**
   * 处理点击重试翻译
   * @param item
   */
  handleClickRetryTranslation = async item => {
    let ticketId = item.ticketId;
    let messageId = item.id;
    let content = item.content || item.translate_content;
    let role = item.role;
    // 更新指定会话的指定消息的翻译内容
    const updatedSessionList = this.state.globalList.map(session => {
      if (session.ticketId === ticketId) {
        session.messageList = session.messageList.map(message =>
          message.id === messageId
            ? { ...message, translationStatus: 1 }
            : message,
        );
      }
      return session;
    });
    // 更新数据
    this.setState({
      globalList: updatedSessionList,
    });
    // 如果是客户的
    if (role === 'customer') {
      this.handleGetTranslateText(
        ticketId,
        messageId,
        content,
        this.state.currentSessionData.translateCode,
        role,
        updatedSessionList,
      );
    } else {
      // 如果是客服的
      if (this.state.currentSessionData.customerSourceLanguageCode) {
        let translateResponse = await this.handleGetTranslateText(
          ticketId,
          messageId,
          content,
          this.state.currentSessionData.customerSourceLanguageCode,
          'agent',
          updatedSessionList,
        );
        if (translateResponse && translateResponse.code === 200) {
          this.updateSessionBySessionId(
            this.state.currentSessionData.ticketId,
            'customerSourceLanguageCode',
            translateResponse.data.translatedText,
          );
          this.handleIMWebClientSend(
            this.state.currentSessionData.ticketId,
            translateResponse.data.translatedText,
          );
        }
      }
    }
  };

  // 保存im发过来的数据，传给工单记录
  handleSaveImData = messageData => {
    //加入三方会话后需要对"是否为本用户发送的消息"进行判重，只有本用户发送的才调用replyMessage防止重复。
    //join_chat消息不需要判重,也就是系统消息

    if (
      messageData.username === this.props.user.userName ||
      messageData.type === 'system' ||
      messageData.role == 'customer'
    ) {
      console.log(messageData, '查contentID');
      // if (messageData.type === 'role' && messageData.role === 'customer') {
      //   messageData.chatUserName = messageData.username;
      // } else if (
      //   messageData.type === 'role' &&
      //   ['agent', 'agent_admin'].includes(messageData.role)
      // ) {
      messageData.chatUserName = messageData.username;
      // }
      messageData.channelId = this.state.channelConfigId;
      const transferParams = handleSaveImParams(messageData);
      //transferParams格式为im需要的数据
      this.props.dispatch({
        type: 'worktable/replyMessage',
        payload: transferParams,
        callback: response => {
          if (response) {
            let { code, data, msg } = response;
            if (code === 200) {
              //这里处理回复完消息倒计时刷新
              if (data) {
                let scheduleObj = data;
                let newList = this.state.globalList?.map(item => {
                  let temp = { ...item };
                  if (
                    scheduleObj &&
                    item.ticketId === scheduleObj.workRecordId
                  ) {
                    temp.timeStatus = scheduleObj?.timeStatus;
                    temp.scheduleTime = scheduleObj?.scheduleTime;
                    temp.timeType = scheduleObj?.timeType;
                  }
                  return temp;
                });
                this.setState(
                  {
                    globalList: newList,
                  },
                  () => {
                    this.cacheSessionList('globalList', this.state.globalList);
                  },
                );
              }
            } else {
              notification.error({
                message: getIntl().formatMessage({
                  id: 'worktable.reply.fail',
                }),
              });
            }
          }
        },
      });
    }
  };

  /**
   * 处理IM服务端断开连接响应
   * @param data
   */
  handleImConnectCloseEvent = data => {
    console.log('=====结束聊天client_close');
    // 判断状态码是否为成功状态
    if (data.code === IMWebClient.config.CODE_OK) {
      // 判断消息是否为特定动作
      if (data.message === IMWebClient.config.sessionAction.action_1000) {
        // 断开事件的会话id
        const closeTicketId = data.data.sessionId;
        const updatedGlobalList = [...this.state.globalList];
        //点击结束后更新对话框状态
        updatedGlobalList.map(item =>
          item.ticketId === closeTicketId ? { ...item, ticketStatus: 2 } : item,
        );
        this.setState({
          globalList: updatedGlobalList,
        });

        // 断开事件的客户id、名称，这里需要循环data.data.user
        const customerId = data.data?.uid;
        const customerName = data.data?.username;
        //结束时调用工单接口记录结束时机，用于倒计时
        let addChannelContactDetailPayload = {
          contactId: data.data.contactId,
          companyId: this.props.user.companyId,
          workOrderId: data.data.sessionId,
          eventType: '6',
        };
        this.props.dispatch({
          type: 'worktable/addChannelContactDetail',
          payload: addChannelContactDetailPayload,
        });
        // 添加系统消息-退出
        this.handleAddMessageNew(
          {
            ticketId: closeTicketId,
            contactId: data.data.contactId,
            uid: customerId,
            id: data.data?.id ? data.data?.id : uuidv4(),
            type: 'system',
            role: 'system',
            content: getIntl().formatMessage(
              { id: 'im.chat.left' },
              { leftChat: customerName },
            ),
            contentType: 'text',
          },
          this.props.user.roleList?.[0]?.roleId === '1005' ? false : true,
        );
        this.updateSessionBySessionId(data.data.sessionId, 'ticketStatus', 2);
        if (closeTicketId === this.state.currentSessionData.ticketId) {
          this.setState(preState => ({
            currentSessionData: {
              ...preState.currentSessionData,
              ticketStatus: 2,
            },
          }));
        }

        // 打开智能总结弹框
        // this.openAiIntelligenceSumModal();
        // this.acwOpenAiIntelligenceSumModal();
      }
    }
  };

  /**
   * 处理IM聊天得到消息已读响应:当前会话状态变为已读，其余未读+1
   * @param data
   */
  handleImMessageReadEvent = data => {
    // 已读事件的会话id
    const readTicketId = data.data.sessionId;
    // 当前会话id
    const currentSessionId = this.state.ticketId;
    // 获取当前的会话列表
    const sessionIndex = this.state.globalList?.findIndex(
      f => f.ticketId === currentSessionId,
    );

    if (sessionIndex === -1) {
      return;
    }
    let updatedGlobalList = [...this.state.globalList];
    let updatedGlobalListNew = updatedGlobalList.map(tItem => {
      if (tItem.ticketId === readTicketId) {
        // 更新messageList中的每个元素的状态
        tItem.messageList = tItem.messageList.map(m => ({
          ...m,
          status: 1,
        }));
        //处理已读后数据改变但是页面未改变，解决办法：触发currentSessionData变化
        console.log(
          'handleImMessageReadEvent',
          this.state.currentSessionData,
          tItem,
        );
        if (tItem?.selected) {
          this.setState({
            currentSessionData: tItem,
          });
        }
      }
      return tItem;
    });

    this.setState(
      {
        globalList: updatedGlobalListNew,
      },
      () => {
        this.cacheSessionList('globalList', updatedGlobalListNew);
      },
    );
  };
  /**
   * 得到服务端座席是否在线的状态，同步到客户端
   * @param data
   */
  handleGetAgentStatus = data => {
    console.log(data, 'handleGetAgentStatus');
    if (data.code == '200') {
      if (
        data.data.status == '1' &&
        (this.props.agentStatus?.split('_')?.[0] === '0' ||
          this.props.websocketStatus === false)
      ) {
        IMWebClient.changeStatus({
          tenantId: this.props.user.companyId,
          teamId: this.props.user.deptId,
          uid: this.props.user.userId,
          statusType: '0',
          statusName: this.props.agentStatusName,

          // statusName: getIntl().formatMessage({
          //   id: 'im.agent.status.offline',
          //   defaultMessage: 'Offline',
          // }),
          role:
            this.props.user.roleList?.[0]?.roleId === '1005'
              ? 'agent_admin'
              : 'agent',
        });
      } else if (
        data.data.status == '0' &&
        this.props.agentStatus?.split('_')?.[0] === '1' &&
        this.props.websocketStatus === true
      ) {
        IMWebClient.changeStatus({
          tenantId: this.props.user.companyId,
          teamId: this.props.user.deptId,
          uid: this.props.user.userId,
          statusType: '1',
          statusName: this.props.agentStatusName,

          // statusName: getIntl().formatMessage({
          //   id: 'im.agent.status.available',
          //   defaultMessage: 'Available',
          // }),
          role:
            this.props.user.roleList?.[0]?.roleId === '1005'
              ? 'agent_admin'
              : 'agent',
        });
      }
    }
  };
  /**
   * 处理消息撤回事件
   * @param data
   */
  handleMessageWithdrawEvent = data => {
    console.log(data, 'handleMessageWithdrawEvent');
    if (data.code === IMWebClient.config.CODE_OK) {
      //contentId
      const contentId = data.data.contentId;
      // 工单id
      const readTicketId = data.data.ticketId;
      // 获取当前的会话列表
      const sessionIndex = this.state.globalList?.findIndex(
        f => f.ticketId === readTicketId,
      );
      if (sessionIndex === -1) {
        return;
      }
      this.setState(
        preState => {
          let updatedGlobalList = [...preState.globalList];
          let updatedGlobalListNew = updatedGlobalList.map(tItem => {
            if (tItem.ticketId === readTicketId) {
              // 更新messageList中的每个元素的状态
              tItem.messageList = tItem.messageList.map(m => {
                if (m.id === contentId) {
                  return {
                    ...m,
                    dataStatus: '999', //撤回
                  };
                } else if (m.referenceContentId === contentId) {
                  //这里处理引用的消息被撤回的情况
                  return {
                    ...m,
                    referenceContent: getIntl().formatMessage({
                      id: 'workerOffers.withdraw.message.tips.1',
                    }),
                    referenceContentType: '1',
                    referenceReplyPerson: '',
                  };
                } else {
                  return m;
                }
              });
              if (tItem?.selected) {
                this.setState({
                  currentSessionData: tItem,
                });
              }
            }
            return tItem;
          });
          return {
            globalList: updatedGlobalListNew,
          };
        },
        () => {
          this.cacheSessionList('globalList', this.state.globalList);
        },
      );
      //向工单记录此条消息被撤回
      this.updateContentDetails(readTicketId, contentId);
    }
  };
  //向工单记录此条消息被撤回
  updateContentDetails = (readTicketId, contentId) => {
    let list = [];
    list.push(contentId);
    this.props.dispatch({
      type: 'worktable/updateContentDetails',
      payload: {
        workRecordId: readTicketId,
        contentId: list,
        content: getIntl().formatMessage({
          id: 'workerOffers.withdraw.message.tips.1',
        }), //修改的消息体
        replyPerson: '', //修改的发送人
        contentType: 1,
      },
    });
  };
  /**
   * 处理正在输入事件
   * @param data
   */
  handleBeInputting = data => {
    console.log(data, 'handleBeInputting');
    if (data.code === IMWebClient.config.CODE_OK) {
      const ticketId = data.data.ticketId;
      if (this.state.currentSessionData?.ticketId === ticketId) {
        this.setState({
          isBeInputting: true,
        });
      } else {
        this.setState({
          isBeInputting: false,
        });
      }
      // 清除之前的定时器（如果存在）
      if (this.inputtingTimer) {
        clearTimeout(this.inputtingTimer);
      }

      // 设置新的定时器，2秒后将 isBeInputting 设置回 false
      this.inputtingTimer = setTimeout(() => {
        this.setState({
          isBeInputting: false,
        });
      }, 2000);
    }
  };
  /**
   * 处理关闭ACW响应：客户关闭，主动点击按钮关闭
   * @param data
   */
  handleImCloseAcwEvent = data => {
    if (data.code === IMWebClient.config.CODE_OK) {
      // 断开事件的会话id
      const closeTicketId = data.data.sessionId;
      const updatedGlobalList = [...this.state.globalList];
      updatedGlobalList.map(item =>
        item.ticketId === closeTicketId ? { ...item, ticketStatus: 3 } : item,
      );
      this.setState({
        globalList: updatedGlobalList,
      });
      this.updateSessionBySessionId(data.data.sessionId, 'ticketStatus', 3);
      this.setState({
        currentSessionData: {
          ...this.state.currentSessionData,
          ticketStatus: 3,
        },
      });
      // 关闭的会话id
      // const closeTicketId = data.data.sessionId;
      // // 移除关闭的会话数据
      // const updatedSessionList = this.state.globalList.filter(
      //   session => session.ticketId !== closeTicketId,
      // );
      // // 默认切换下一条
      // if (updatedSessionList && updatedSessionList.length > 0) {
      //   this.handleCurrentSession(updatedSessionList[0]);
      // }
      // // 更新当前会话数据、会话列表数据
      // this.setState({ globalList: updatedSessionList });
    }
  };
  /**
   * 满意度评价回馈
   * @param data
   */
  handleImCustomerEvaluationEvent = data => {
    console.log(data, 'customer_evaluation');
    if (data.code === IMWebClient.config.CODE_OK) {
      // 断开事件的会话id
      const sessionId = data.data.ticketId;
      // 添加系统消息-退出
      this.handleAddMessageNew(
        {
          ticketId: sessionId,
          contactId: data.data.contactId,
          uid: data.data.uid,
          id: data.data?.id ? data.data?.id : uuidv4(),
          type: 'system',
          role: 'system',
          content: data.data.starCount,
          contentType: 'evaluation',
        },
        false,
      );
    }
  };
  /**
   * 处理im返回acw状态
   * @param data 消息数据
   */
  handleImConnectBindEvent = data => {
    let ticketId = data?.data?.sessionId;
    console.log(data, IMWebClient.config.CODE_OK, 'handleImConnectBindEvent');
    if (data.code === IMWebClient.config.CODE_OK) {
      // 如果是ACW状态
      if (data.message === IMWebClient.config.sessionAction.action_1005) {
        this.updateSessionBySessionId(ticketId, 'ticketState', 1);
        this.setState({
          currentSessionData: {
            ...this.state.currentSessionData,
            ticketStatus: 1,
          },
        });
      }
      //ws连接成功记录状态
      this.props.dispatch({
        type: 'layouts/setWebsocketStatusNewAw',
        payload: true,
      });
    } else if (data.code === IMWebClient.config.sessionAction.action_5001) {
      // 如果超出允许在线人数
      this.props.dispatch({
        type: 'layouts/setAllowOnlineCount',
        payload: false,
      });
      notification.error({
        message: data.message,
      });
      //下线操作
      let index = this.props.agentStatusList.find(
        item => item.type === '0' && item.initStatus === '1',
      );
      this.props.dispatch({
        type: 'layouts/setAgentStatus',
        payload: index.type + '_' + index.agentStatusId,
      });
      this.props.dispatch({
        type: 'layouts/setAgentStatusName',
        payload: index.agentStatusName,
      });

      return;
    } else {
      //重连操作
      // let s = 0;
      // switch (IMWebClient.bindCount) {
      //   case 0:
      //     s = 1000;
      //     break;
      //   case 1:
      //     s = 3000;
      //     break;
      //   case 2:
      //     s = 15000;
      //     break;
      // }
      // setTimeout(() => {
      //   IMWebClient.bindCount = IMWebClient.bindCount + 1;
      //   IMWebClient.connect();
      //   //15秒后允许重连
      //   if (s === 15000) {
      //     //ws连接成功记录状态
      //     this.props.dispatch({
      //       type: 'layouts/setAllowReconnect',
      //       payload: true,
      //     });
      //   } else {
      //     this.props.dispatch({
      //       type: 'layouts/setAllowReconnect',
      //       payload: false,
      //     });
      //   }
      // }, s);
      // let newGlobalList = this.state.globalList?.filter(
      //   item => item.ticketId !== ticketId,
      // );
      // this.setState({
      //   globalList: newGlobalList,
      // });
    }
  };

  // 发送消息给im
  handleIMWebClientSend = (
    ticketId,
    value,
    options = {},
    mergeDefaults = true,
  ) => {
    const defaultOptions = {
      sessionId: ticketId,
      contentType: 'text',
      content: value,
      tenantId: this.props.user.companyId,
      role:
        this.props.user.roleList?.[0]?.roleId === '1005'
          ? 'agent_admin'
          : 'agent',
    };
    const sendOptions = mergeDefaults
      ? { ...defaultOptions, ...options }
      : options;
    IMWebClient.send({
      ...sendOptions,
    });
  };

  // 发送满意度给im
  handleIMWebClientInviteRatings = ticketId => {
    // 发送消息到IM服务端
    IMWebClient.inviteRatings({
      sessionId: ticketId,
      tenantId: this.props.user.companyId,
    });
  };

  // 上传文件给im
  handleIMWebClientGetUploadPreSignedUrl = async (fileName, fileType, date) => {
    const result = await IMWebClient.getUploadPreSignedUrl(
      fileName,
      fileType,
      this.props.user.companyId,
      date,
    );
    return result;
  };

  /**
   * 缓存会话列表数据
   * @param list
   */
  cacheSessionList = (key, list) => {
    localStorage.setItem(key, JSON.stringify(list));
  };

  /**
   * 更新当前会话数据
   * @param propertyKey 字段
   * @param value 值
   */
  updateCurrentSession = (propertyKey, value) => {
    if (!propertyKey) {
      return;
    }
    let { currentSessionData } = this.state;
    if (currentSessionData) {
      currentSessionData[propertyKey] = value;
    } else {
      // 默认IM聊天数据
      let data = {
        userId: this.props.user.userId,
        userName: this.props.user.userName,
        lastName: this.props.user.lastName,
        companyId: this.props.user.companyId,
        id: '',
        uid: null,
        username: null,
        role: '',
        type: '',
        contentType: '',
        content: '',
        fileName: '',
        status: 1, //已读状态
        time: '',
        translationStatus: false,
        translationContent: '',
        uploadProgress: 0,
      };
      this.setState({
        currentSessionData: data,
      });
    }
    // 缓存当前会话
    localStorage.setItem(
      'currentSessionData',
      JSON.stringify(this.state.currentSessionData),
    );
  };
  /**
   * 更新所有会话数据
   * @param sessionId 会话id
   * @param propertyKey 字段
   * @param value 值
   */
  updateSessionBySessionId = (ticketId, propertyKey, value) => {
    // 更新会话状态
    let list = this.state.globalList;
    list.forEach(item => {
      if (item.ticketId === ticketId) {
        item[propertyKey] = value;
      }
    });

    this.setState({ globalList: list }, () => {
      console.log('handleCurrentSession', this.state.globalList);
      this.cacheSessionList('globalList', this.state.globalList);
    });
  };

  // 点击左边每个会话事件（获取最新当前会话数据和缓存存储）
  handleCurrentSession = data => {
    // 邮件tab
    if (this.props.workTableTabValue === 3) {
      let addData = {
        ticketId: data?.workRecordId, // 工单id
        ticketStatus: data?.sessionStatus || 1, // 工单状态 1：进行中，2：ACW
        ticketCode: data?.wordRecordCode, // 工单编号
        uid: data?.customerId, // 客户id
        username: data?.customerName, // 客户名称
      };
      // let newItem = this.state.globalList;
      let totalData = { ...data, ...addData };
      localStorage.setItem('currentSessionData', JSON.stringify(totalData));
      this.setState({
        // globalList: totalData,
        ticketId: data.workRecordId,
        channelConfigId: data.channelConfigId,
        currentSessionData: totalData,
      });
    } else {
      //  chat聊天tab ---》处理中
      if (+this.props.workTableTabProcesOrPendValue === 1) {
        console.log('handleCurrentSession', this.state.globalList);
        let newItem = [...this.state.globalList];
        localStorage.setItem('currentSessionData', JSON.stringify(data));
        newItem?.forEach((item, index) => {
          if (item.ticketId === data.ticketId) {
            item.selected = true;
            item.unReadCount = 0;
          } else {
            item.selected = false;
          }
        });
        this.setState(
          {
            globalList: newItem,
            ticketId: data.ticketId,
            channelConfigId: data.channelId,
            currentSessionData: data,
          },
          () => {
            this.cacheSessionList('globalList', newItem);
            //掉线后再上线获得离线消息
            if (this.state.currentSessionData && data.ticketId) {
              // 更新会话
              IMWebClient.offlineMessage({
                tenantId: this.props.user.companyId,
                sessionId: data.ticketId,
                uid: this.props.user.userId,
              });
            }
          },
        );
      } else if (+this.props.workTableTabProcesOrPendValue === 2) {
        //排队中、待分配;分配完工单清空会话窗口
        localStorage.setItem('currentSessionData', JSON.stringify({}));
        this.setState({
          ticketId: '',
          channelConfigId: '',
          currentSessionData: {},
        });
      }
    }
  };

  // chat领取---》未分配下每个会话事件
  handleSessionItemPendingClick = data => {
    if (+this.props.workTableTabProcesOrPendValue === 2) {
      let newItem = this.state.handledPendingList;
      newItem?.forEach((item, index) => {
        if (item.ticketId === data.ticketId) {
          item.selected = true;
          item.status = 0;
        } else {
          item.selected = false;
        }
      });
      this.setState({
        globalList: newItem,
        ticketId: data.ticketId,
        channelConfigId: data.channelId,
        currentSessionData: data,
      });
    }
  };
  // 查看机器人聊天详情
  handleSessionItemRobotClick = data => {
    if (+this.props.workTableTabProcesOrPendValue === 3) {
      let newItem = this.state.robotListParent;
      newItem?.forEach((item, index) => {
        if (item.ticketId === data.ticketId) {
          item.selected = true;
          item.status = 0;
        } else {
          item.selected = false;
        }
      });
      this.setState({
        globalList: newItem,
        ticketId: data.ticketId,
        channelConfigId: data.channelId,
        currentSessionData: data,
      });
    }
  };
  // acw状态打开智能总结弹框
  acwOpenAiIntelligenceSumModal = async () => {
    await this.props.dispatch({
      type: 'worktable/saveAcwAiIntelligenceSumModal',
      payload: true,
    });
    this.openAiIntelligenceSumModal();
  };
  // 切换tab时的事件
  handleChangeTab = () => {
    localStorage.setItem('currentSessionData', JSON.stringify({}));
    this.setState({
      // globalList: totalData,
      ticketId: '',
      channelConfigId: '',
      currentSessionData: {},
    });
  };
  // 打开智能总结
  openAiIntelligenceSumModal = () => {
    this.setState({
      isShowModal: true,
      serviceSummaryModal: true,
    });
  };

  // 点击按钮打开智能总结
  // openAiIntelligenceSumModalBtn = () => {
  //   this.setState({
  //     isShowModalBtn: true,
  //     serviceSummaryModal: true,
  //   });
  // };
  // 关闭智能总结弹框
  closeAiIntelligenceSumModal = () => {
    this.setState({
      serviceSummaryModal: false,
      isShowModal: false,
      // isShowModalBtn: false,
    });
  };
  // 智能总结结果 - 确定添加消息列表
  getItelligentSummaryContent = async params => {
    const { summary, mood, toDoList } = params;
    let { currentSessionData } = this.state;
    let messageData = {
      ticketId: currentSessionData.ticketId,
      contactId: currentSessionData.contactId,
      id: uuidv4(),
      content: '',
      contentType: 'text',
      role: 'system',
      type: 'aisummary',
      username: this.props.user.userName,
      uid: currentSessionData.uid,
      translationContent: '', // 翻译的内容
      translationStatus: '',
    };
    messageData.content = {
      summary: summary,
      mood: +mood,
      toDoList: toDoList,
    };
    messageData.content = JSON.stringify(messageData.content);
    // 添加消息数据到到消息列表中
    this.handleAddMessageNew(messageData);
    this.setState({
      serviceSummaryModal: false,
      isShowModal: false,
      // isShowModalBtn: false,
    });
  };

  // 处理选择翻译语言的变化事件
  handleTranslationCodeChange = (ticketId, e) => {
    this.updateSessionBySessionId(ticketId, 'translateCode', e);
  };

  /**
   * 处理未分配下pendingList变化数据
   */
  setWaitPendingList = async e => {
    let newList = e;
    let newPendingList = newList?.map((item, index) => {
      let mList = item?.ticketContentIndex?.map(content => {
        return {
          id: content.work_record_content_id,
          uid: '',
          username: content.reply_person,
          role: content.reply_type,
          type: content.reply_type,
          contentType: content.content_type,
          content: content.content,
          fileName: content.content,
          filePath: content.ticket_file?.[0]?.file_path,
          status: 1,
          time: content.reply_time,
          translationStatus: 0,
          translationContent: content.translate_content,
          translateLanguage: content.translate_language,
          uploadProgress: 0,
          ticket_file: content.ticket_file,
          ticketId: content.work_record_id,
          //下面是进线意图
          incomingIntentName: content.incoming_intent_name,
          intelligentAgentName: content.intelligent_agent_name,
          //下面是客户进线原因+深度思考
          inboundIntent: content.inbound_intent,
          deepThought: content.deep_thought,
          //引用
          referenceContentId: content.reference_work_record_content_id,
          referenceReplyPerson: content.reference_reply_person,
          referenceContent: content.reference_content,
          referenceContentType: content.reference_content_type,
          referenceContentFile: content.reference_content_file,

          //是否撤回
          dataStatus: content.data_status,
        };
      });
      return {
        ticketId: item.workRecordId, // 工单id
        ticketStatus: item.sessionStatus || 1, // 工单状态 1：进行中，2：ACW
        ticketCode: item.wordRecordCode, // 工单编号
        ticketLevel: item.priorityLevelId, // 工单等级
        ticketLevelName: item.priorityLevelName, // 工单等级Name
        ticketTime: item.createTime, // 工单时间
        ticketType: item.workRecordTypeId, // 工单类型
        uid: item.customerId, // 客户id
        username: item.customerName, // 客户名称
        channelId: item.channelTypeId, //渠道id
        channelConfigId: item.channelConfigId, //渠道id
        channelType: item.channelTypeName, // 渠道类型
        customerId: item.customerId,

        invitationEvaluationStatus: null, // 是否发送邀请
        translateStatus: false, // 是否开启翻译
        translateCode: '', // 翻译目标Code
        customerSourceLanguageCode: '', // 源语言
        unReadCount: 0, // 消息未读数量
        selected: false, //选中状态
        agentId: item.agentId,
        deptId: item.deptId,
        originalStatus: item.oldWorkRecordCode !== null ? true : false, //判断是否已转单
        messageList: mList,
      };
    });
    let result = await this.insertAvatorColor(newPendingList);
    this.setState({ handledPendingList: result });
  };
  /**
   * 处理未分配下pendingList变化数据
   */
  setRobotListParent = async e => {
    let newList = e;
    let newRobotListParent = newList?.map((item, index) => {
      let mList = item?.ticketContentIndex?.map(content => {
        return {
          id: content.work_record_content_id,
          uid: '',
          username: content.reply_person,
          role: content.reply_type,
          type: content.reply_type,
          contentType: content.content_type,
          content: content.content,
          fileName: content.content,
          filePath: content.ticket_file?.[0]?.file_path,

          status: 1,
          time: content.reply_time,
          translationStatus: 0,
          translationContent: content.translate_content,
          translateLanguage: content.translate_language,
          uploadProgress: 0,
          ticket_file: content.ticket_file,
          ticketId: content.work_record_id,
          //下面是进线意图
          incomingIntentName: content.incoming_intent_name,
          intelligentAgentName: content.intelligent_agent_name,
          //下面是客户进线原因+深度思考
          inboundIntent: content.inbound_intent,
          deepThought: content.deep_thought,
          //引用
          referenceContentId: content.reference_work_record_content_id,
          referenceReplyPerson: content.reference_reply_person,
          referenceContent: content.reference_content,
          referenceContentType: content.reference_content_type,
          referenceContentFile: content.reference_content_file,

          //是否撤回
          dataStatus: content.data_status,
        };
      });
      return {
        ticketId: item.workRecordId, // 工单id
        ticketStatus: item.sessionStatus || 1, // 工单状态 1：进行中，2：ACW
        ticketCode: item.wordRecordCode, // 工单编号
        ticketLevel: item.priorityLevelId, // 工单等级
        ticketLevelName: item.priorityLevelName, // 工单等级Name
        ticketTime: item.createTime, // 工单时间
        ticketType: item.workRecordTypeId, // 工单类型
        uid: item.customerId, // 客户id
        username: item.customerName, // 客户名称
        channelId: item.channelTypeId, //渠道id
        channelConfigId: item.channelConfigId, //渠道id
        channelType: item.channelTypeName, // 渠道类型
        customerId: item.customerId,

        invitationEvaluationStatus: null, // 是否发送邀请
        translateStatus: false, // 是否开启翻译
        translateCode: '', // 翻译目标Code
        customerSourceLanguageCode: '', // 源语言
        unReadCount: 0, // 消息未读数量
        selected: false, //选中状态
        agentId: item.agentId,
        deptId: item.deptId,
        originalStatus: item.oldWorkRecordCode !== null ? true : false, //判断是否已转单
        messageList: mList,
      };
    });
    let result = await this.insertAvatorColor(newRobotListParent);

    this.setState({ robotListParent: result });
  };
  //更新左右栏宽度，解决中间栏撑开问题
  getRightWidth = e => {
    this.setState({
      rightWidth: e,
    });
  };
  getLeftWidth = e => {
    this.setState({
      leftWidth: e,
    });
  };
  //插入电话工单id
  getPhoneInfo = e => {
    console.log(e, 'getPhoneInfo===e');
    if (e.workRecordId) {
      this.queryByTicketRemarks(e);
    } else {
      this.setState(
        prevState => {
          return {
            ticketId: '',
            phoneNumberId: '',
            currentSessionData: {},
          };
        },
        () => {
          localStorage.setItem('currentSessionData', JSON.stringify({}));
        },
      );
    }
    console.log('最顶层拿到电话工单信息', e);
  };
  // 查询备注
  queryByTicketRemarks = e => {
    let params = {
      workRecordId: e?.workRecordId,
      pageSize: 10000,
      pageNum: 1,
    };
    // 查询工单备注列表接口
    this.props.dispatch({
      type: 'workOrderCenter/queryByTicketRemarksNew',
      payload: params,
      callback: response => {
        this.setState({
          loadingDetail: false,
        });
        if (response.code == 200) {
          let phoneInfo = {
            ticketId: e?.workRecordId, // 工单id
            ticketStatus: 3, // 工单状态 1：进行中，2：ACW
            ticketCode: e?.wordRecordCode, // 工单编号
            ticketLevel: e?.priorityLevelId, // 工单等级
            ticketLevelName: e?.priorityLevelName, // 工单等级Name
            ticketTime: e?.createTime, // 工单时间
            ticketType: e?.workRecordTypeId, // 工单类型
            uid: e?.customerId, // 客户id
            username: e?.customerName ? e?.customerName : e?.voiceName, // 客户名称
            channelId: e?.channelTypeId, //渠道id
            channelConfigId: e?.channelConfigId, //渠道id
            channelType: e?.channelTypeName, // 渠道类型
            invitationEvaluationStatus: false, // 是否发送邀请
            translateStatus: false, // 是否开启翻译
            translateCode: '', // 翻译目标Code
            customerSourceLanguageCode: '', //
            unReadCount: 0, // 消息未读数量
            selected: true, //选中状态
            agentId: e?.agentId,
            deptId: e?.deptId,
            originalStatus: e?.oldWorkRecordCode !== null ? true : false, //判断是否已转单
            phoneNumber: e?.phoneNumber,
            phoneAcwStatus: e?.phoneAcwStatus,
            messageList: [],
          };
          let data = [...response.data.records];
          phoneInfo.messageList = data
            ?.map(item => {
              return {
                id: e?.ticketContentIndex?.work_record_content_id,
                uid: null,
                username: item.operatorName,
                role: 'system',
                type: 'remark',
                contentType: 'text',
                content: item.operationLogReason,
                fileName: '',
                status: 1, //已读状态
                time: item.createTime,
                translationStatus: '',
                translationContent: '',
                uploadProgress: 0,
              };
            })
            .reverse();

          this.setState({
            ticketId: phoneInfo.ticketId,
            phoneNumberId: phoneInfo.ticketId,
            currentSessionData: phoneInfo,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 聊天框内点击当前电话
  handleClickCurrentPhone = value => {
    this.chatListRef.current?.handleCallOut(value);
  };
  getKnowledgeAnswerCopy = e => {
    this.props.dispatch({
      type: 'worktable/copyAnswerToWorkTable',
      payload: e,
    });
  };

  getUser = () => {
    this.props.dispatch({
      type: 'layouts/getUser1',
      callback: response => {
        if (response.code == 200) {
          let roleList = response.data.roleList;
          let roleId = roleList[0].roleId;
          this.setState({
            roleId: roleId,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 处理IM服务端用户状态变化响应，上线离线
   * @param data
   */
  handleImChangeStatusEvent = data => {
    console.log('handleImChangeStatusEvent', data);
    if (data.code === IMWebClient.config.CODE_OK) {
      // 调用工单事件函数 - 座席状态切换事件
      try {
        this.imChatStateChange(data.data.statusType);
      } catch (e) {
        console.log('IMChat 调用工单事件函数异常 [座席状态切换事件]');
      }
    }
  };

  /**
   * 处理来电响应
   * @param data
   */
  handleImIncomingCallEvent = async data => {
    console.log('handleImIncomingCallEvent', data, this.state.pendingMessages);
    // 如果是接听类型
    if (+data.code === 200) {
      if (data.data.type === 'receive') {
        // 设置标记，表示正在处理来电
        sessionStorage.setItem('isProcessingCall', 'true');
        const sessionId = data.data.sessionId;
        const contactId = data.data.contactId;
        this.setState({
          ticketId: sessionId,
          contactId: contactId,
        });
        // 更新会话
        await this.queryProcessingWorkOrderList(sessionId, contactId);
      } else if (data.data.type === 'join_chat') {
        //客户加入聊天保存到工单记录里，该类型不需要判重或者判断角色，im推给谁谁就调用
        const newMessage = {
          ticketId: data.data.sessionId,
          id: data.data.id ? data.data.id : uuidv4(), // id
          contactId: data.data.contactId,
          time: getCurrentDateTime(), // 时间
          type: 'system', // 消息类型，role：用户消息, system：系统消息
          role: 'system', // 角色，system：系统，customer：客户，agent：坐席
          uid: '', // 用户id
          username: data.data.username, // 用户名称
          contentType: 'text', // 消息内容类型，text，link，image，video
          content: getIntl().formatMessage(
            { id: 'im.chat.join' },
            { joinChat: data.data.username },
          ), // 消息内容，text时是文字，link、image、video时都是链接
        };
        this.handleAddMessageNew(newMessage);
        // this.handleSaveImData(newMessage);
      }
    }
  };
  //     入参:
  // "workOrderId"工单id
  // "workRecordTypeCode":修改的工单类型
  // "priorityLevelId":"修改的优先级id
  queryProcessingWorkOrDerListSchedule = (
    payload,
    list = this.state.globalList,
  ) => {
    this.props.dispatch({
      type: 'worktable/queryProcessingWorkOrDerListSchedule',
      payload: payload,
      callback: response => {
        if (response.code == 200) {
          let scheduleList = response.data;
          let newList = list?.map(item => {
            let temp = item;
            scheduleList.forEach(schedule => {
              if (schedule.workRecordId === temp.ticketId) {
                temp.timeStatus = schedule?.timeStatus;
                temp.scheduleTime = schedule?.scheduleTime;
                temp.timeType = schedule?.timeType;
              }
            });
            return temp;
          });
          console.log('queryProcessingWorkOrDerListSchedule', newList);
          this.UpdateLatestData(
            newList,
            JSON.parse(localStorage.getItem('globalList')),
            'ticketId',
            'invitationEvaluationStatus',
            'translateStatus',
            'translateCode',
            'customerSourceLanguageCode',
          );
          // this.setState(
          //   {
          //     globalList: newList,
          //   },
          //   () => {
          //     this.cacheSessionList('globalList', this.state.globalList);
          //   },
          // );
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**
   * 处理中的会话列表
   * channelType：渠道类型代号，从下拉列表那个code中选择
      search：搜索关键字
      orderType ：排序类型：1-按消息到达时间倒序 2-按消息到达时间正序 3-按回复消息时间倒序 4-按回复消息时间正序 （默认是1）
   */
  queryProcessingWorkOrderList = async (value, cId) => {
    this.props.dispatch({
      type: 'worktable/queryProcessingWorkOrderList',
      payload: {
        data: {
          workOrderId: value,
        },
        page: {
          pageSize: 10,
          pageNum: 1,
        },
      },
      callback: async response => {
        if (response.code == 200) {
          let res = response.data.rows?.[0];

          //获取客户头像颜色
          let row = await this.getCustomerAvatorColor(res);
          console.log('getCustomerAvatorColor', row);
          //客户加入聊天保存到工单记录里
          const newMessage = {
            ticketId: row?.workRecordId,
            contactId: cId,
            id: uuidv4(), // id
            time: getCurrentDateTime(), // 时间
            type: 'system', // 消息类型，role：用户消息, system：系统消息
            role: 'system', // 角色，system：系统，customer：客户，agent：坐席
            uid: row?.customerId, // 用户id
            username: row?.customerName, // 用户名称
            status: 0,
            contentType: 'text', // 消息内容类型，text，link，image，video
            content: getIntl().formatMessage(
              { id: 'im.chat.join' },
              { joinChat: row?.customerName },
            ), // 消息内容，text时是文字，link、image、video时都是链接
          };
          // 如果是座席管理员，则不需要replyMessage消息，防止重复
          if (
            !this.props.collectTicketStatus &&
            this.props.user.roleList?.[0]?.roleId !== '1005'
          ) {
            this.handleSaveImData(newMessage);
          }
          //创建新工单格式
          let sessionData = {
            ticketId: row?.workRecordId, // 工单id
            contactId: cId, //联系id
            customerAvatorColor: row?.customerAvatorColor, //头像颜色
            ticketStatus: row?.sessionStatus || 1, // 工单状态 1：进行中，2：ACW
            ticketCode: row?.wordRecordCode, // 工单编号
            ticketLevel: row?.priorityLevelId, // 工单等级
            ticketLevelName: row?.priorityLevelName, // 工单等级Name
            ticketTime: row?.createTime, // 工单时间
            ticketType: row?.workRecordTypeId, // 工单类型
            uid: row?.customerId, // 客户id
            username: row?.customerName, // 客户名称
            channelId: row?.channelTypeId, //渠道id
            channelConfigId: row?.channelConfigId, //渠道id
            channelType: row?.channelTypeName, // 渠道类型
            customerId: row?.customerId,
            invitationEvaluationStatus: false, // 是否发送邀请
            translateStatus:
              localStorage.getItem('worktable_setting_translation') === 'true', // 是否开启翻译
            translateCode: localStorage.getItem(
              'worktable_setting_translation_code',
            ), // 翻译目标Code
            customerSourceLanguageCode: '', //
            unReadCount:
              this.state.globalList?.length <= 0 ||
              (this.state.globalList?.length > 0 &&
                value === this.state.currentSessionData.ticketId)
                ? 0
                : 1, // 消息未读数量: 领取成功或者新的聊天加入默认展示一条
            selected:
              this.state.globalList?.length <= 0 ||
              (this.state.globalList?.length > 0 &&
                value === this.state.currentSessionData.ticketId)
                ? true
                : false, //选中状态
            agentId: row?.agentId,
            deptId: row?.deptId,
            originalStatus: row?.oldWorkRecordCode !== null ? true : false, //判断是否已转单
            isAdminJoinChat: row?.isAdminJoinChat ?? false, //判断座席管理员是否在聊天群组中
            messageList: [],
            isTop: row?.isTop, //是否置顶
          };
          sessionData.messageList = row?.ticketContentIndex?.map(content => {
            return {
              id: content.work_record_content_id,
              uid: '',
              username: content.reply_person,
              role: content.reply_type,
              type: content.reply_type,
              contentType: content.content_type,
              content: content.content,
              fileName: content.content,
              filePath: content.ticket_file?.[0]?.file_path,

              status: 1, //已读状态
              time: content.reply_time,
              translationStatus: content.translate_content ? 2 : 0,
              translationContent: content.translate_content,
              translateLanguage: content.translate_language,
              uploadProgress: 0,
              ticket_file: content.ticket_file,
              ticketId: content.work_record_id,
              //下面是进线意图
              incomingIntentName: content.incoming_intent_name,
              intelligentAgentName: content.intelligent_agent_name,
              //下面是客户进线原因+深度思考
              inboundIntent: content.inbound_intent,
              deepThought: content.deep_thought,
              //引用
              referenceContentId: content.reference_work_record_content_id,
              referenceReplyPerson: content.reference_reply_person,
              referenceContent: content.reference_content,
              referenceContentType: content.reference_content_type,
              referenceContentFile: content.reference_content_file,

              //是否撤回
              dataStatus: content.data_status,
            };
          });
          sessionData.messageList.push(newMessage);
          console.log('获取到IM消息 新加入聊天获取到item', sessionData);

          if (sessionData.selected) {
            this.setState({
              currentSessionData: sessionData,
              channelConfigId: row?.channelTypeId,
            });
          }
          //更新会话
          this.setState(
            prevState => {
              let updatedSessionList = [...prevState.globalList];
              let num = 0;

              //这里如果是在已有列表匹配到，需要过滤掉
              updatedSessionList = updatedSessionList.filter(session => {
                if (session?.isTop === '2') {
                  num++;
                }
                return session.ticketId !== value;
              });

              // 将匹配到的session置顶到置顶会话后
              if (sessionData && sessionData?.isTop === '2') {
                updatedSessionList.unshift(sessionData);
              } else if (
                sessionData &&
                (sessionData?.isTop === '1' || !sessionData?.isTop)
              ) {
                updatedSessionList.splice(num, 0, sessionData);
              }

              return {
                globalList: updatedSessionList,
              };
            },
            () => {
              this.queryProcessingWorkOrDerListSchedule(
                {
                  workOrderId: '',
                  workRecordTypeCode: '',
                  priorityLevelId: '',
                },
                this.state.globalList,
              );
              this.cacheSessionList('globalList', this.state.globalList);
              sessionStorage.setItem('isProcessingCall', 'false');

              // 处理期间缓存的消息
              const { pendingMessages } = this.state;
              if (pendingMessages.length > 0) {
                // 按接收顺序处理所有暂存的消息
                for (const pendingMsg of pendingMessages) {
                  this.handleIMChatIncomingMessage(pendingMsg);
                }
                // 清空已处理的缓存消息并恢复标记
                this.setState({
                  pendingMessages: [],
                });
              }
            },
          );
          //更新列表对话总量
          this.chatListRef.current?.setToTalNum(true);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 整合对比缓存中的messageList和接口返回的messageList

  getGlobalListFromLocalStorage = (value, row) => {
    // 从localStorage获取globalList数组
    const storedGlobalList = localStorage.getItem('globalList');
    let parsedGlobalList = [];
    try {
      parsedGlobalList = storedGlobalList ? JSON.parse(storedGlobalList) : [];
    } catch (error) {
      console.error('解析localStorage中的globalList失败:', error);
    }

    // 在globalList中查找ticketId为value的数据项
    const existingSession = parsedGlobalList.find(
      item => item.ticketId === value,
    );

    // 如果找到了匹配的会话数据
    if (existingSession && existingSession.messageList && row?.messageList) {
      // 创建一个映射，用于快速查找现有消息
      const existingMessagesMap = {};
      existingSession.messageList.forEach(msg => {
        existingMessagesMap[msg.id] = msg;
      });

      // 找出row中独有的消息
      const uniqueMessages = row.messageList.filter(rowMsg => {
        // 检查id是否存在于现有消息中
        return !existingMessagesMap[rowMsg.id];
      });

      // 如果有新消息，将它们添加到现有会话的messageList中
      if (uniqueMessages.length > 0) {
        // 按照时间顺序插入新消息
        const updatedMessageList = [...existingSession.messageList];

        uniqueMessages.forEach(newMsg => {
          // 找到合适的插入位置（按时间排序）
          let insertIndex = updatedMessageList.length;
          for (let i = 0; i < updatedMessageList.length; i++) {
            if (new Date(newMsg.time) < new Date(updatedMessageList[i].time)) {
              insertIndex = i;
              break;
            }
          }
          // 插入消息
          updatedMessageList.splice(insertIndex, 0, newMsg);
        });
        existingSession.messageList = updatedMessageList;
      }
      return existingSession;
    } else {
      return row;
    }
  };
  /**
   * 座席状态切换事件
   * @param data
   */
  imChatStateChange = data => {
    // data = JSON.parse(data);
    let state = data;
    //available
    if (state == '1') {
      // 掉线后再上线获得离线消息
      console.log(
        'IMChat StateChange',
        data,
        this.state.currentSessionData.ticketId,
        this.state.globalList,
      );
      if (
        this.state.currentSessionData.ticketId ||
        this.state.globalList?.length > 0
      ) {
        IMWebClient.offlineMessage({
          tenantId: this.props.user.companyId,
          sessionId:
            this.state.currentSessionData.ticketId ??
            this.state.globalList[0].ticketId,
          uid: this.props.user.userId,
        });
      }
      // this.props.dispatch({
      //   type: 'global/changeConnectState',
      //   payload: 'topLine',
      // });
    } else if (state == '0') {
      //offline
      // this.props.dispatch({
      //   type: 'global/changeConnectState',
      //   payload: 'downLine',
      // });
    }
  };

  /**
   * chatlist发生变化，更新globalList
   */
  setGlobalList = async e => {
    let newList = e;
    let newGlobalList = newList?.map((item, index) => {
      let mList = item?.ticketContentIndex?.map(content => {
        return {
          id: content.work_record_content_id,
          uid: '',
          username: content.reply_person,
          role: content.reply_type,
          type: content.reply_type,
          contentType: content.content_type,
          content: content.content,
          fileName: content.content,
          filePath:
            +content.content_type === 4
              ? content.ticket_file?.[0]?.file_path
              : content.content,
          status: 1,
          time: content.reply_time,
          translationStatus: content.translate_content ? 2 : 0,
          translationContent: content.translate_content,
          translateLanguage: content.translate_language,
          uploadProgress: 0,
          ticket_file: content.ticket_file,
          ticketId: content.work_record_id,
          //下面是进线意图
          incomingIntentName: content.incoming_intent_name,
          intelligentAgentName: content.intelligent_agent_name,
          //下面是客户进线原因+深度思考
          inboundIntent: content.inbound_intent,
          deepThought: content.deep_thought,
          //引用
          referenceContentId: content.reference_work_record_content_id,
          referenceReplyPerson: content.reference_reply_person,
          referenceContent: content.reference_content,
          referenceContentType: content.reference_content_type,
          referenceContentFile: content.reference_content_file,

          //是否撤回
          dataStatus: content.data_status,
        };
      });
      return {
        ticketId: item.workRecordId, // 工单id
        ticketStatus: item.sessionStatus || 1, // 工单状态 0:未分配，1：进行中，2：ACW
        ticketCode: item.wordRecordCode, // 工单编号
        ticketLevel: item.priorityLevelId, // 工单等级
        ticketLevelName: item.priorityLevelName, // 工单等级Name
        ticketTime: item.createTime, // 工单时间
        ticketType: item.workRecordTypeId, // 工单类型
        uid: item.customerId, // 客户id
        username: item.customerName, // 客户名称
        channelId: item.channelTypeId, //渠道id
        channelConfigId: item.channelConfigId, //渠道id
        channelType: item.channelTypeName, // 渠道类型
        customerId: item.customerId,
        invitationEvaluationStatus: null, // 是否发送邀请
        translateStatus: false, // 是否开启翻译
        translateCode: '', // 翻译目标Code

        customerSourceLanguageCode: '', // 源语言
        unReadCount: 0, // 消息未读数量
        selected: false, //选中状态
        agentId: item.agentId,
        deptId: item.deptId,
        originalStatus: item.oldWorkRecordCode !== null ? true : false, //判断是否已转单
        isAdminJoinChat: item?.isAdminJoinChat ?? false, //判断座席管理员是否在聊天群组中
        timeStatus: item?.timeStatus, //当前时间状态 是否超时  1 - 超出，2 - 剩余
        scheduleTime: item.scheduleTime, // 时间
        timeType: item.timeType, // 时间类型   1 - 响应  2-解决
        messageList: mList,
        isTop: item?.isTop, //是否置顶
      };
    });
    let result = await this.insertAvatorColor(newGlobalList);
    //整合缓存和列表接口数据，目前只拿缓存中的invitationEvaluationStatus,补充翻译状态translateStatus，translateCode
    if (result && result?.length > 0) {
      this.UpdateLatestData(
        result,
        JSON.parse(localStorage.getItem('globalList')),
        'ticketId',
        'invitationEvaluationStatus',
        'translateStatus',
        'translateCode',
        'customerSourceLanguageCode',
        'unReadCount',
        'isAdminJoinChat',
        // 'messageList',
      );
    } else {
      this.setState({ globalList: result });
    }
  };

  /**
   * 整合缓存和列表接口数据，拿缓存中的invitationEvaluationStatus，translateStatus，translateCode，customerSourceLanguageCode，unReadCount。因为后端无法实时更新，所以前端搞
   */
  UpdateLatestData = (
    array1,
    array2,
    key,
    invitationEvaluationStatus,
    translateStatus,
    translateCode,
    customerSourceLanguageCode,
    unReadCount,
    isAdminJoinChat,
    // messageList,
  ) => {
    const updatedArray1 = array1.map(item1 => {
      const item2 = array2?.find(item => item[key] === item1[key]);
      if (item2) {
        return {
          ...item1,
          [invitationEvaluationStatus]: item2[invitationEvaluationStatus], // 更新状态
          [translateStatus]: item2[translateStatus], // 更新状态
          [translateCode]: item2[translateCode], // 更新状态
          [customerSourceLanguageCode]: item2[customerSourceLanguageCode],
          [unReadCount]: item2[unReadCount], // 更新状态
          [isAdminJoinChat]: item2[isAdminJoinChat], // 更新状态
          // [messageList]: [...item2[messageList], ...item1.messageList],
        };
      } else {
        return item1; // 保持原对象不变
      }
    });
    console.log(array1, array2, updatedArray1, 'updatedArray1===333');
    this.setState({ globalList: updatedArray1 }, () => {
      this.cacheSessionList('globalList', this.state.globalList);
    });
  };
  /**
   * 循环插入用户头像颜色属性
   */
  insertAvatorColor = async list => {
    return new Promise(resolve => {
      let resultList = [...list];
      this.props.dispatch({
        type: 'avatorService/queryCustomerPhotoColor',
        callback: response => {
          if (response.code == 200) {
            let colorList = response.data;
            resultList = list?.map(item => {
              let result = { ...item };
              colorList?.forEach(color => {
                if (result.customerId === color.customerId) {
                  result.customerAvatorColor = color.photoColor;
                }
              });
              return result;
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
          resolve(resultList);
        },
      });
    });
  };
  /**
   * 单个获取客户头像颜色
   */
  getCustomerAvatorColor = async res => {
    return new Promise(resolve => {
      let resultList = { ...res };
      this.props.dispatch({
        type: 'avatorService/queryCustomerPhotoColor',
        callback: response => {
          if (response.code == 200) {
            let colorList = response.data;
            colorList?.forEach(color => {
              if (res.customerId === color.customerId) {
                resultList.customerAvatorColor = color.photoColor;
              }
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
          resolve(resultList);
        },
      });
    });
  };
  /**
   * 整合离线消息，接收子组件messageList数据
   */
  // setGlobalMessageList = list => {
  //   const updatedArray1 = this.state.globalList?.map(item1 => {
  //     if (list.ticketId === item1.ticketId) {
  //       return {
  //         ...item1,
  //         messageList: list,
  //       };
  //     } else {
  //       return item1;
  //     }
  //   });
  //   console.log('setGlobalMessageList', list, updatedArray1);
  //   this.setState({ globalList: updatedArray1 }, () => {
  //     this.cacheSessionList('globalList', this.state.globalList);
  //   });
  // };
  render() {
    const {
      globalList,
      ticketId,
      currentSessionData,
      channelConfigId,
      isShowModal,
      // isShowModalBtn,
      serviceSummaryModal,
    } = this.state;

    return (
      <div className={styles.worktableBox}>
        {/* <WorkOrderDetailContent
            closeWorkOrderDetail={this.closeWorkOrderDetail}
            workOrderDetail={workOrderDetail}
            roleId={this.shandleIMWebClientSendtate.roleId}
          /> */}
        <div className={styles.worktableContent}>
          <ChatList
            ref={this.chatListRef}
            handleChangeTab={this.handleChangeTab}
            handleSessionItemClick={e => this.handleCurrentSession(e)}
            handleAddMessageNew={this.handleAddMessageNew}
            handleGetTranslateText={this.handleGetTranslateText}
            globalList={globalList}
            setGlobalList={e => this.setGlobalList(e)}
            num={globalList.length}
            handledPendingList={this.state.handledPendingList}
            setWaitPendingList={e => this.setWaitPendingList(e)}
            robotListParent={this.state.robotListParent}
            setRobotListParent={e => this.setRobotListParent(e)}
            handleSessionItemPendingClick={e =>
              this.handleSessionItemPendingClick(e)
            }
            handleSessionItemRobotClick={e =>
              this.handleSessionItemRobotClick(e)
            }
            handleSetTopTicket={e => this.handleSetTopTicket(e)}
            handleSetNoTopTicket={e => this.handleSetNoTopTicket(e)}
            onLeftWidth={e => this.getLeftWidth(e)}
            getPhoneInfo={e => this.getPhoneInfo(e)}
          />
          {+this.props.workTableTabValue !== 4 && (
            <ChatLayout
              handleClickCurrentPhone={value =>
                this.handleClickCurrentPhone(value)
              }
              ref={this.imContainerRef}
              deleteTicket={this.deleteTicket}
              handleTranslationCodeChange={this.handleTranslationCodeChange}
              handleTranslationChange={this.handleTranslationChange}
              openAiIntelligenceSumModal={this.openAiIntelligenceSumModal}
              handleSaveImData={this.handleSaveImData}
              handleAddMessageNew={this.handleAddMessageNew}
              handleAddNoteNew={this.handleAddNoteNew}
              handleGetTranslateText={this.handleGetTranslateText}
              handleClickRetryTranslation={this.handleClickRetryTranslation}
              channelConfigId={channelConfigId}
              ticketId={ticketId}
              phoneNumberId={this.state.phoneNumberId}
              globalList={globalList}
              updateCurrentSession={this.updateCurrentSession}
              updateSessionBySessionId={this.updateSessionBySessionId}
              currentSessionData={currentSessionData}
              sessionList={this.state.sessionList}
              handleIMWebClientSend={this.handleIMWebClientSend}
              handleIMWebClientInviteRatings={
                this.handleIMWebClientInviteRatings
              }
              handleIMWebClientGetUploadPreSignedUrl={
                this.handleIMWebClientGetUploadPreSignedUrl
              }
              queryProcessingWorkOrDerListSchedule={
                this.queryProcessingWorkOrDerListSchedule
              }
              // setGlobalMessageList={this.setGlobalMessageList}
              leftWidth={this.state.leftWidth}
              rightWidth={this.state.rightWidth}
              isBeInputting={this.state.isBeInputting}
            />
          )}
          {isShowModal && (
            <AiIntelligenceSummary
              deleteTicket={this.deleteTicket}
              currentSessionData={this.state.currentSessionData}
              serviceSummaryModal={serviceSummaryModal}
              getItelligentSummaryContent={this.getItelligentSummaryContent}
              closeAiIntelligenceSumModal={this.closeAiIntelligenceSumModal}
            />
          )}

          {/* 用户主动点击ai智能总结按钮情况 */}
          {/* {isShowModalBtn && (
            <AiIntelligenceSummaryBtn
              currentSessionData={this.state.currentSessionData}
              serviceSummaryModal={serviceSummaryModal}
              getItelligentSummaryContent={this.getItelligentSummaryContent}
              closeAiIntelligenceSumModal={this.closeAiIntelligenceSumModal}
            />
          )} */}

          {+this.props.workTableTabValue !== 4 && (
            <FunctionalPlate
              handleClickCurrentPhone={value =>
                this.handleClickCurrentPhone(value)
              }
              currentSessionData={this.state.currentSessionData}
              onRightWidth={e => this.getRightWidth(e)}
              id={this.state.currentSessionData?.ticketId}
              length={this.state.currentSessionData.messageList?.length}
              globalList={globalList}
              getKnowledgeAnswerCopy={e => this.getKnowledgeAnswerCopy(e)}
            />
          )}
        </div>
      </div>
    );
  }
}
const mapStateToProps = ({ worktable, layouts, configuration }) => {
  return {
    ...worktable,
    ...layouts,
    ...configuration,
  };
};
export default umiConnect(mapStateToProps)(WorktableContent);
