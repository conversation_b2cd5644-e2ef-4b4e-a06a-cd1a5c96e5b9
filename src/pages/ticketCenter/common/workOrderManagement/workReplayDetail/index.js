import React, { Component, useEffect, useState, useRef } from 'react';
import { connect, FormattedMessage, getIntl, Link } from 'umi';
import {
  Input,
  Button,
  Checkbox,
  Spin,
  Row,
  Col,
  Form,
  Select,
  Modal,
  Switch,
  Tag,
  Radio,
  Collapse,
  Tabs,
  Card,
  Carousel,
  Image,
  Popover,
} from 'antd';
import styles from './index.less';
import ReactMarkdown from 'react-markdown';
import { RightOutlined, LeftOutlined } from '@ant-design/icons';
import { parsePhoneNumberFromString } from 'libphonenumber-js';
import { v4 as uuidv4 } from 'uuid';
const { Panel } = Collapse;

import ReturnImg from '../../../../../assets/return.png';
import EmailUser from '../../../../../assets/email-user-icon.png';
import AddressImg from '../../../../../assets/addressee-icon.png';
import AttachmentImg from '../../../../../assets/attachment-outlined-icon.png';
import CustomerService from '../../../../../assets/customer-service.png';
import UserOnline from '../../../../../assets/user-online.png';
import PhoneVoiceIcon from '../../../../../assets/phone-voice.png';
import DownArrowIcon from '../../../../../assets/down-arrow-icon.png';
import TranslateIcon from '../../../../../assets/translate-icon.png';
import VoiceDataIcon from '../../../../../assets/voice-data-icon.png';

import AgentIcon from '../../../../../assets/agent.svg';
import CustomIcon from '../../../../../assets/custom.svg';
import HideContentIcon from '../../../../../assets/down-arrow-icon.png';

import AppVideoOutlinedIcon from '../../../../../assets/AppVideoOutlined.svg';
import WebVideoOutlinedIcon from '../../../../../assets/WebVideoOutlined.svg';

import AudioPlayer from '../AudioPlayer';
import { notification } from '../../../../../utils/utils';
import { history } from '../../../../../.umi/core/history';
import NoDataImg from '../../../../../assets/no-data-img.jpg';
import ChatVoiceIcon from '../../../../../assets/chat-icon.jpg';
import MoodGoodActiveIcon from '../../../../../assets/mood-good-active.png';
import MoodGoodIcon from '../../../../../assets/mood-good.png';
import MoodNormalActiveIcon from '../../../../../assets/mood-normal-active.png';
import MoodNormalIcon from '../../../../../assets/mood-normal.png';
import MoodBadActiveIcon from '../../../../../assets/mood-bad-active.png';
import MoodBadIcon from '../../../../../assets/mood-bad.png';
import AiIntentionIcon from '../../../../../assets/ai-intention-icon.png';
import NewUpIcon from '../../../../../assets/new-up-icon.png';
import NewDownIcon from '../../../../../assets/new-down-icon.png';
import TestImg from '../../../../../assets/7.png';
import {
  AgentUserIcon,
  AttachmentIcon,
  DocumentFileIcon,
  DownloadFileIcon,
} from '../../../../workTableUpgrade/chatLayout/icon';
import {
  DocumentIcon,
  DownloadIcon,
  HotspotIssuesIcon,
  LeftArrowIcon,
  RightArrowIcon,
} from './icon';
const PhoneCallIcon = () => (
  <svg
    style={{ marginTop: '2px', float: 'left', marginRight: '3px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.36377 1.91277L6.24013 4.33095L6.26559 4.36731C6.35408 4.48388 6.41724 4.61765 6.45104 4.76004C6.49307 4.91391 6.50419 5.07458 6.48377 5.23277C6.45747 5.39248 6.40331 5.54634 6.32377 5.68731L6.30195 5.71277C6.22018 5.83459 6.11952 5.94261 6.00377 6.03277L5.9674 6.05458L5.05468 6.7164C5.00547 6.84644 4.99133 6.98712 5.01366 7.12435C5.036 7.26157 5.09404 7.39051 5.18195 7.49822C5.5886 8.06833 6.04493 8.60132 6.54559 9.09095C7.04259 9.58237 7.57892 10.0324 8.14922 10.4364C8.25747 10.5248 8.38743 10.5825 8.52557 10.6036C8.66371 10.6247 8.80498 10.6084 8.93468 10.5564L9.61831 9.61822C9.71319 9.48301 9.83753 9.3711 9.98195 9.29095C10.1198 9.21105 10.2716 9.158 10.4292 9.13458H10.451C10.6031 9.11036 10.7586 9.12156 10.9056 9.16731H10.9383C11.082 9.20707 11.2158 9.27643 11.331 9.37095L13.7238 11.2728L13.7601 11.3091C13.8827 11.4084 13.983 11.5323 14.0547 11.6728C14.1314 11.8242 14.1748 11.9904 14.182 12.16C14.1892 12.3313 14.1607 12.5022 14.0983 12.6619C14.0368 12.8233 13.9413 12.9696 13.8183 13.0909C13.6409 13.2556 13.4472 13.4018 13.2401 13.5273H13.2147C12.9643 13.6718 12.7014 13.7935 12.4292 13.8909C11.0811 14.3173 9.63061 14.2917 8.29831 13.8182C6.81144 13.2864 5.4657 12.422 4.36377 11.2909C3.23088 10.1868 2.37168 8.83328 1.85468 7.33822C1.37071 6.01842 1.33239 4.57641 1.74559 3.23277C1.83647 2.94639 1.95841 2.67081 2.10922 2.41095C2.23466 2.20502 2.38088 2.0125 2.54559 1.8364C2.66587 1.71028 2.81065 1.61005 2.97104 1.54186H3.01104C3.15905 1.47851 3.31924 1.44871 3.48013 1.45459C3.65349 1.46017 3.82352 1.50361 3.97831 1.58186H4.00377L4.36377 1.91277ZM5.45468 4.93459L3.60377 2.5164C3.58902 2.4925 3.5675 2.47352 3.54195 2.46186C3.51515 2.44739 3.48514 2.43989 3.45468 2.44004C3.42695 2.43596 3.39877 2.43596 3.37104 2.44004C3.33547 2.45256 3.30228 2.471 3.27286 2.49459C3.15633 2.62395 3.05052 2.76259 2.9565 2.90913C2.83978 3.10634 2.74584 3.31617 2.6765 3.53458C2.3226 4.67786 2.35455 5.9058 2.76741 7.02913C3.2398 8.37755 4.01765 9.59845 5.04013 10.5964C6.03935 11.6156 7.25826 12.3929 8.60377 12.8691C9.72851 13.2812 10.9573 13.3131 12.102 12.96C12.3111 12.8908 12.5132 12.8019 12.7056 12.6946C12.8622 12.6014 13.0074 12.4904 13.1383 12.3637C13.1629 12.3449 13.1817 12.3198 13.1929 12.2909C13.1967 12.2608 13.1967 12.2302 13.1929 12.2C13.1938 12.166 13.1863 12.1323 13.171 12.1019C13.1609 12.0795 13.1445 12.0606 13.1238 12.0473L10.7092 10.1709C10.6889 10.1551 10.6653 10.1439 10.6401 10.1382C10.6137 10.1328 10.5865 10.1328 10.5601 10.1382C10.5315 10.1424 10.5035 10.1497 10.4765 10.16C10.4529 10.1787 10.431 10.1994 10.411 10.2219L9.68377 11.2364C9.6397 11.2962 9.58556 11.3479 9.52377 11.3891C9.22978 11.5668 8.88831 11.6499 8.54555 11.6271C8.2028 11.6044 7.87531 11.4769 7.6074 11.2619C6.9844 10.8316 6.40252 10.3447 5.86922 9.80731C5.33553 9.27061 4.8489 8.68908 4.41468 8.06913C4.20271 7.79955 4.07771 7.4719 4.05628 7.12963C4.03485 6.78737 4.11799 6.44668 4.29468 6.15277C4.32379 6.09617 4.36622 6.04751 4.41831 6.01095L5.41831 5.28368L5.45468 5.25459C5.47521 5.24176 5.49361 5.22581 5.50922 5.20731C5.52443 5.17052 5.53066 5.13063 5.5274 5.09095C5.53115 5.0632 5.53115 5.03507 5.5274 5.00731C5.52147 4.98514 5.51161 4.9642 5.49831 4.94549L5.45468 4.93459ZM8.57831 3.88731H11.4183L10.0801 5.22913C10.0273 5.28465 9.99827 5.35855 9.99907 5.43516C9.99987 5.51176 10.0305 5.58504 10.0844 5.63945C10.1383 5.69385 10.2114 5.72511 10.288 5.72657C10.3645 5.72804 10.4387 5.69961 10.4947 5.64731L12.3383 3.80004C12.3383 3.80004 12.3383 3.77095 12.3638 3.76731C12.367 3.75541 12.367 3.74285 12.3638 3.73095V3.5164V3.48004C12.3689 3.46098 12.3689 3.44092 12.3638 3.42186L10.4947 1.54186C10.4676 1.51437 10.4353 1.49254 10.3998 1.47764C10.3642 1.46274 10.326 1.45507 10.2874 1.45507C10.2488 1.45507 10.2106 1.46274 10.1751 1.47764C10.1395 1.49254 10.1072 1.51437 10.0801 1.54186C10.0517 1.56839 10.0291 1.60049 10.0136 1.63614C9.9981 1.6718 9.99011 1.71026 9.99011 1.74913C9.99011 1.788 9.9981 1.82646 10.0136 1.86212C10.0291 1.89778 10.0517 1.92987 10.0801 1.9564L11.4183 3.29822H8.60377C8.52623 3.30007 8.45238 3.33169 8.39754 3.38654C8.34269 3.44138 8.31107 3.51523 8.30922 3.59277C8.30922 3.63145 8.31684 3.66975 8.33164 3.70548C8.34645 3.74122 8.36814 3.77369 8.39549 3.80104C8.42284 3.82839 8.45532 3.85009 8.49105 3.86489C8.52679 3.87969 8.56509 3.88731 8.60377 3.88731H8.57831Z"
      fill="white"
    />
  </svg>
);
const ExportIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
  >
    <path
      d="M23.7883 18.8292C23.4104 18.8351 23.1057 19.1456 23.1116 19.5236V21.2667C23.1116 22.0284 22.5081 22.6495 21.7639 22.6495H8.29614C7.552 22.6495 6.94849 22.0284 6.94849 21.2667V8.8302C6.94849 8.06848 7.552 7.44739 8.29614 7.44739H12.9836L14.7239 10.5441C14.8645 10.7902 15.1282 10.9161 15.3889 10.8868H21.761C22.5051 10.8868 23.1086 11.5079 23.1086 12.2697V13.9982C23.1086 14.382 23.4133 14.6925 23.7883 14.6925C24.1633 14.6925 24.4651 14.382 24.4651 13.9982C24.4651 13.9806 24.4651 13.963 24.4622 13.9454V12.2697C24.4622 10.7433 23.2551 9.50403 21.7668 9.50403H15.6995L13.9622 6.41907C13.8069 6.14075 13.5842 6.06751 13.3879 6.07336L13.385 6.07043H8.28735C6.79907 6.07043 5.59204 7.30969 5.59204 8.83606V21.2374C5.59204 22.7638 6.79907 24.0031 8.28735 24.0031H21.7668C23.2551 24.0031 24.4622 22.7638 24.4622 21.2374V19.5763C24.4651 19.5587 24.4651 19.5411 24.4651 19.5236C24.468 19.3419 24.3977 19.1661 24.2688 19.0372C24.1428 18.9054 23.97 18.8322 23.7883 18.8292Z"
      fill="#3463FC"
    />
    <path
      d="M16.6007 13.8049C16.7091 13.6877 16.8585 13.6203 17.0197 13.6203C17.1779 13.6203 17.3302 13.6877 17.4386 13.8049L19.9347 16.4621L19.9493 16.4767H19.9523L19.9669 16.4914C20.1925 16.7404 20.1925 17.1213 19.9669 17.3703L17.462 20.0422C17.3566 20.1594 17.2072 20.2238 17.049 20.2267C16.8907 20.2267 16.7413 20.1623 16.6359 20.0451L16.6241 20.0305C16.3986 19.7814 16.3986 19.4006 16.6241 19.1516L18.1329 17.5402H11.2804C10.9611 17.5402 10.7003 17.2619 10.7003 16.9221C10.7003 16.5822 10.9611 16.3039 11.2804 16.3039H18.1066L16.6007 14.6955C16.3693 14.4465 16.3693 14.0568 16.6007 13.8049Z"
      fill="#3463FC"
    />
  </svg>
);

class WorkReplayDetailContent extends Component {
  formAssignWorkOrderRef = React.createRef();
  formLanguageRef = React.createRef();
  formLanguageRef1 = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      transcriptList: [],
      transcriptText: [],
      loadingReplyText: true,
      translatePhoneStatus: false,
      answerText: '',
      newContentList: [],
      isModalOpen: false,
      languageList: [
        {
          value: '中文',
          label: '中文（Chinese)',
        },
        {
          value: 'English',
          label: 'English',
        },
        {
          value: '한국어',
          label: '한국어 (Korean)',
        },
        {
          value: '日本語',
          label: '日本語 (Japanese)',
        },
        {
          value: 'Français',
          label: 'Français (French)',
        },
        {
          value: 'Italiano',
          label: 'Italiano (Italian)',
        },
        {
          value: 'Deutsch',
          label: 'Deutsch (German)',
        },
        {
          value: 'Español',
          label: 'Español (Spanish)',
        },

        {
          value: 'Português',
          label: 'Português (Portuguese)',
        },
        {
          value: 'Bahasa',
          label: 'Bahasa Indonesia',
        },
        {
          value: 'हिन्दी',
          label: 'हिन्दी (Hindi)',
        },
        {
          value: 'Tiếng Việt',
          label: 'Tiếng Việt (Vietnamese)',
        },

        {
          value: 'Русский',
          label: 'Русский (Russian)',
        },
        {
          value: 'Українська',
          label: 'Українська (Ukrainian)',
        },
      ],
      defaultLanguage: '',
      chatVoice: '',
      transcriptListInit: [],
      transcriptTextInit: [],
      translateType: '',
      downRecordingFile: '',
    };
  }
  componentDidUpdate(prevProps, prevState, snapshot) {
    let {
      initTranscriptList,
      initTranscriptText,
      initTranslateType,
      ticketContentList,
      newWorkOrderDetail,
    } = this.props;
    if (
      prevState.transcriptListInit !== initTranscriptList &&
      prevState.transcriptList.length == 0
    ) {
      this.setState({
        transcriptList: initTranscriptList,
        transcriptListInit: initTranscriptList,
        transcriptText: initTranscriptText,
        transcriptTextInit: initTranscriptText,
        translateType: initTranslateType,
        loadingReplyText: false,
      });
    }
    if (prevState.newContentList !== ticketContentList) {
      this.setState({
        newContentList: ticketContentList,
        chatVoice: newWorkOrderDetail.chatVoice,
      });
    }
  }

  componentDidMount() {
    let lang = localStorage.getItem('lang');
    if (lang) {
      if (lang == 'zh-CN') {
        this.setState({
          defaultLanguage: '中文',
          language: '中文',
        });
      } else if (lang == 'en-US') {
        this.setState({
          defaultLanguage: 'English',
          language: 'English',
        });
      }
    }
    this.getUserRoleId();
    // this.setState({
    //   roleId: this.props.user?.roleList[0]?.roleId,
    // });
  }
  // 获取角色ID
  getUserRoleId = () => {
    this.props.dispatch({
      type: 'layouts/getUser1',
      callback: response => {
        if (response.code == 200) {
          let roleList = response.data.roleList;
          let roleId = roleList[0].roleId;

          this.setState({
            roleId: roleId,
            downRecordingFile: response.data.downRecordingFile,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 下载附件
  downLoadFile = (bucketName, fileUrl) => {
    let params = {
      fileUrl: fileUrl,
      bucketName: bucketName,
    };
    this.props.dispatch({
      type: 'workOrderCenter/queryDownload',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          const eleLink = document.createElement('a');
          eleLink.style.display = 'none';
          // eleLink.target = "_blank"
          eleLink.href = response.data;
          // eleLink.href = record;
          document.body.appendChild(eleLink);
          eleLink.click();
          document.body.removeChild(eleLink);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 电话语音转文字
  handleVoiceAnalysis = contactId => {
    if (contactId) {
      this.setState({
        loadingReplyText: true,
      });
      this.props.dispatch({
        type: 'workOrderCenter/queryVoiceAnalysis',
        payload: contactId,
        callback: response => {
          if (response.code == 200) {
            if (response.data) {
              let transcriptList = response.data.translateVOS;
              // for (let i = 0; i < transcriptList.length; i++) {
              //   transcriptList[i].translatePhoneContent = null;
              // }
              this.setState({
                transcriptList: transcriptList,
                transcriptText: response.data.text,
                translateType: response.data.translateType,
                loadingReplyText: false,
              });
            } else {
              this.setState({
                transcriptList: [],
                transcriptText: [],
                translateType: '',
                loadingReplyText: false,
              });
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    }
  };

  // 一键回拨
  jumpToWorkTable = () => {
    // 1--获取电话  2---获取邮箱
    let customerId = this.props.newWorkOrderDetail.customerId;
    let params = {
      customerId: customerId,
      type: 1,
    };
    this.props.dispatch({
      type: 'customerInformationManagement/getDetailsByType',
      payload: params,
      callback: res => {
        if (res.code === 200) {
          let phoneNumber = res.data;
          this.props.dispatch({
            type: 'layouts/setPhoneNumber',
            payload: phoneNumber,
          });
          this.props.dispatch({
            type: 'layouts/workerTablePush',
            payload: true,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 智能翻译
  queryAnswer = params => {
    let { transcriptList } = this.state;
    this.props.dispatch({
      type: 'workOrderCenter/queryAnswer',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            let answerText = response.data.text;
            // this.setState({
            //   answerText: answerText,
            //   translatePhoneStatus: true,
            // });
            let newTranscriptList = JSON.stringify(transcriptList);
            let newTranscriptList1 = JSON.parse(newTranscriptList);
            for (let i = 0; i < newTranscriptList1.length; i++) {
              if (newTranscriptList1[i].id == params.workRecordContentId) {
                newTranscriptList1[i].translatePhoneContent = answerText;
              }
            }
            this.setState({
              transcriptList: newTranscriptList1,
              transcriptListInit: newTranscriptList1,
              loadingReplyText: false,
            });
          } else {
            this.setState({
              // transcriptList: [],
              loadingReplyText: false,
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  queryChatAnswer = params => {
    // let lang = localStorage.getItem('lang');
    // let language = '';
    // if (lang == 'zh-CN') {
    //   language = '中文';
    // } else {
    //   language = 'English';
    // }
    // let params = {
    //   promptId: '40000',
    //   contentQuestion: content,
    //   language: language,
    //   wordType: 3,
    //   workRecordContentId: workRecordContentId,
    // };
    this.props.dispatch({
      type: 'workOrderCenter/queryAnswer',
      payload: params,
      callback: response => {
        if (response.code !== 200) {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  queryEmailAnswer = (content, workRecordContentId) => {
    let lang = localStorage.getItem('lang');
    let language = '';
    if (lang == 'zh-CN') {
      language = '中文';
    } else {
      language = 'English';
    }
    let params = {
      promptId: '40000',
      contentQuestion: content,
      language: language,
      wordType: 2,
      workRecordContentId: workRecordContentId,
    };
    this.props.dispatch({
      type: 'workOrderCenter/queryAnswer',
      payload: params,
      callback: response => {
        if (response.code !== 200) {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**
   * 语言选择
   */
  handleSelect = e => {
    this.setState({
      language: e,
    });
  };
  showTextTranslation = (content, workRecordContentId) => {
    this.formLanguageRef.current?.resetFields();
    this.setState({
      isModalOpen: true,
      chatContent: content,
      workRecordContentId: workRecordContentId,
    });
  };
  handleCancelTextTranslation = () => {
    this.setState({
      isModalOpen: false,
    });
  };
  onFinishTextTranslation = () => {
    let { chatContent, workRecordContentId, language } = this.state;
    let params = {
      promptId: '40000',
      contentQuestion: chatContent,
      language: language,
      // language: 'en-US',
      wordType: 3,
      workRecordContentId: workRecordContentId,
    };
    this.queryChatAnswer(params);
    this.setState({
      isModalOpen: false,
    });
  };
  showTextTranslation1 = (content, workRecordContentId) => {
    this.formLanguageRef1.current?.resetFields();
    this.setState({
      isModalOpen: true,
      phoneContent: content,
      workRecordContentId: workRecordContentId,
    });
  };
  onFinishTextTranslation1 = () => {
    let { language, phoneContent, workRecordContentId } = this.state;
    let params = {
      promptId: '40000',
      contentQuestion: phoneContent,
      language: language,
      wordType: 1,
      workRecordContentId: workRecordContentId,
    };
    this.queryAnswer(params);
    this.setState({
      isModalOpen: false,
    });
  };

  // 隐藏翻译内容
  hideTranslationContent = workRecordContentId => {
    let { transcriptList } = this.state;
    for (let i = 0; i < transcriptList.length; i++) {
      if (transcriptList[i].id == workRecordContentId) {
        transcriptList[i].translatePhoneContent = null;
        this.setState({
          transcriptList: transcriptList,
        });
      }
    }
  };
  hideChatTranslationContent = workRecordContentId => {
    let { ticketContentList } = this.props;
    let { newContentList } = this.state;
    let newTicketContentList = JSON.stringify(newContentList);
    let newTicketContentList1 = JSON.parse(newTicketContentList);
    for (let i = 0; i < ticketContentList.length; i++) {
      if (newTicketContentList1[i].workRecordContentId == workRecordContentId) {
        newTicketContentList1[i].translationContent = null;
        this.setState({
          newContentList: newTicketContentList1,
        });
        this.props.dispatch({
          type: 'workOrderCenter/setTicketContentList',
          payload: newTicketContentList1,
        });
      }
    }
  };

  // 同步录音文件
  syncCallRecord = payload => {
    let params = {
      contactId: payload,
      ticketLanguage: this.props.newWorkOrderDetail.ticketLanguage,
    };
    this.props.dispatch({
      type: 'workOrderCenter/newSyncCallRecord',
      payload: params,
    });
  };

  // 对敏感信息(邮箱和电话)进行脱敏处理
  maskSensitiveInfo = content => {
    if (!content) return content;
    let uuid = uuidv4();
    // 先处理邮箱脱敏
    let result = this.maskEmails(content);
    // 再处理电话号码脱敏
    let resultPhone = this.maskPhoneNumbers(result, uuid);
    if (resultPhone.includes(`__${uuid}__`)) {
      // 查找并替换所有匹配的模式 __uuid__masked__
      const pattern = new RegExp(`__${uuid}__([^_]+)__`, 'g');
      // 使用正则表达式匹配所有实例
      const matches = [...resultPhone.matchAll(pattern)];

      if (matches.length > 0) {
        // 构建带有Popover的JSX结构
        let parts = [];
        let lastIndex = 0;

        matches.forEach(match => {
          // 添加匹配前的文本
          if (match.index > lastIndex) {
            parts.push(resultPhone.substring(lastIndex, match.index));
          }

          // 添加带Popover的脱敏值
          const maskedValue = match[1]; // 获取脱敏后的值
          parts.push(maskedValue);

          // 更新lastIndex为当前匹配结束位置
          lastIndex = match.index + match[0].length;
        });

        // 添加最后一个匹配之后的文本
        if (lastIndex < resultPhone.length) {
          parts.push(resultPhone.substring(lastIndex));
        }

        return <span>{parts}</span>;
      }
      return resultPhone;
    } else {
      return <span>{resultPhone}</span>;
    }
  };

  // 邮箱脱敏处理
  maskEmails = content => {
    if (!content) return content;

    // 邮箱正则表达式 - 匹配文本中的所有邮箱
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;

    return content.replace(emailRegex, email => {
      return this.maskEmail(email);
    });
  };

  // 对单个邮箱进行脱敏
  maskEmail = email => {
    if (!email) return email;
    // 判断是否需要脱敏
    const shouldMaskPhone = this.props.user.workbenchPhoneHide === '1';
    if (!shouldMaskPhone) return email;

    const parts = email.split('@');
    if (parts.length !== 2) return email;

    const localPart = parts[0];
    const domainPart = parts[1];

    // 对本地部分进行脱敏处理
    let maskedLocalPart = '';
    if (localPart.length > 2) {
      maskedLocalPart =
        localPart[0] + '******' + localPart[localPart.length - 1];
    } else {
      maskedLocalPart = localPart[0] + '******';
    }

    // 对域名部分进行脱敏处理
    const domainParts = domainPart.split('.');
    const domainName = domainParts[0];
    const domainSuffix = domainParts.slice(1).join('.');

    let maskedDomainName = '***';
    if (domainName.length > 0) {
      maskedDomainName = '***' + domainName.substr(-3);
    }

    return maskedLocalPart + '@' + maskedDomainName + '.' + domainSuffix;
  };

  // 电话号码脱敏处理
  maskPhoneNumbers = (content, uuid) => {
    if (!content) return content;

    // 判断是否需要脱敏
    const shouldMaskPhone = this.props.user.workbenchPhoneHide === '1';
    if (!shouldMaskPhone) return content;

    // 国际电话号码正则表达式
    const phoneRegex = /(\+\d{1,4})?(\d{5,15})/g;

    return content.replace(
      phoneRegex,
      (match, countryCode = '', phoneNumber) => {
        // 验证是否为有效电话号码
        let validPhoneNumber = false;
        try {
          const fullNumber = (countryCode || '+') + phoneNumber;
          const phoneNumberInfo = parsePhoneNumberFromString(fullNumber);
          validPhoneNumber = phoneNumberInfo?.number;
        } catch (error) {
          validPhoneNumber = false;
        }
        if (
          validPhoneNumber &&
          phoneNumber.length >= 5 &&
          phoneNumber.length <= 15
        ) {
          const maskedNumber =
            match[0] + match[1] + '******' + match[match.length - 1];
          console.log(
            '------------shouldMaskPhone---------------',
            `__${uuid}__${maskedNumber}__`,
          );
          return `__${uuid}__${maskedNumber}__`;
        }
        return match;
      },
    );
  };

  render() {
    let {
      transcriptList,
      transcriptText,
      translateType,
      loadingReplyText,
      translatePhoneStatus,
      answerText,
      isModalOpen,
      newContentList,
      defaultLanguage,
      chatVoice,
      roleId,
      downRecordingFile,
    } = this.state;
    // let { ticketContentList } = this.props;
    if (this.props.channelTypeId == '1') {
      return (
        <Spin spinning={this.props.loadingReply}>
          <div className={styles.workReplayDetailContent}>
            <EmailDetail
              contentList={newContentList}
              downLoadFile={this.downLoadFile}
              queryEmailAnswer={this.queryEmailAnswer}
              user={this.props.user}
            />
          </div>
        </Spin>
      );
    } else if (this.props.channelTypeId == '7') {
      return (
        <Spin spinning={this.props.loadingReply}>
          <div className={styles.workReplayDetailContent}>
            <PhoneDetail
              contentList={newContentList}
              detailList={newContentList}
              handleVoiceAnalysis={this.handleVoiceAnalysis}
              transcriptList={transcriptList}
              transcriptText={transcriptText}
              translateType={translateType}
              jumpToWorkTable={this.jumpToWorkTable}
              loadingReplyText={loadingReplyText}
              queryAnswer={this.queryAnswer}
              translatePhoneStatus={translatePhoneStatus}
              answerText={answerText}
              showTextTranslation1={this.showTextTranslation1}
              hideTranslationContent={this.hideTranslationContent}
              syncCallRecord={this.syncCallRecord}
              roleId={roleId}
              agentAccessChannel={this.props.user.agentAccessChannel}
              downRecordingFile={downRecordingFile}
              maskSensitiveInfo={this.maskSensitiveInfo}
            />
          </div>
          <Modal
            title={getIntl().formatMessage({
              id: 'work.order.reply.test.translate',
              defaultValue: '文本翻译',
            })}
            open={isModalOpen}
            onCancel={this.handleCancelTextTranslation}
            footer={null}
            className="selectLanguageModal"
          >
            <Form
              name="basic"
              autoComplete="off"
              labelAlign="right"
              ref={this.formLanguageRef1}
              onFinish={this.onFinishTextTranslation1}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'work.order.reply.select.language',
                      defaultValue: '选择目标语言：',
                    })}
                    name="language"
                    rules={[
                      {
                        required: false,
                        message: (
                          <FormattedMessage
                            id="work.order.reply.select.language.required"
                            defaultValue="请选择目语言"
                          />
                        ),
                      },
                    ]}
                  >
                    <Select
                      defaultValue={defaultLanguage}
                      options={this.state.languageList}
                      onChange={this.handleSelect}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} style={{ textAlign: 'center' }}>
                <Col span={24}>
                  <Form.Item>
                    <Button
                      style={{ marginRight: '16px' }}
                      onClick={this.handleCancelTextTranslation}
                    >
                      <FormattedMessage
                        id="work.order.management.btn.cancel"
                        defaultMessage="取消"
                      />
                    </Button>
                    <Button type="primary" htmlType="submit">
                      <FormattedMessage
                        id="work.order.management.btn.sure"
                        defaultMessage="确定"
                      />
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </Spin>
      );
    } else if (
      this.props.channelTypeId == '10' ||
      this.props.channelTypeId == '11' ||
      this.props.channelTypeId == '17' ||
      this.props.channelTypeId == '18'
    ) {
      return (
        <Spin spinning={this.props.loadingReply}>
          <div className={styles.workReplayDetailContent}>
            <ChatVoiceDetail
              contentList={newContentList}
              detailList={newContentList}
              handleVoiceAnalysis={this.handleVoiceAnalysis}
              transcriptList={transcriptList}
              transcriptText={transcriptText}
              translateType={translateType}
              jumpToWorkTable={this.jumpToWorkTable}
              loadingReplyText={loadingReplyText}
              queryAnswer={this.queryAnswer}
              translatePhoneStatus={translatePhoneStatus}
              answerText={answerText}
              showTextTranslation1={this.showTextTranslation1}
              hideTranslationContent={this.hideTranslationContent}
              syncCallRecord={this.syncCallRecord}
              channelTypeId={this.props.channelTypeId}
              maskSensitiveInfo={this.maskSensitiveInfo}
            />
          </div>
          <Modal
            title={getIntl().formatMessage({
              id: 'work.order.reply.test.translate',
              defaultValue: '文本翻译',
            })}
            open={isModalOpen}
            onCancel={this.handleCancelTextTranslation}
            footer={null}
            className="selectLanguageModal"
          >
            <Form
              name="basic"
              autoComplete="off"
              labelAlign="right"
              ref={this.formLanguageRef}
              onFinish={this.onFinishTextTranslation1}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'work.order.reply.select.language',
                      defaultValue: '选择目标语言：',
                    })}
                    name="language"
                    rules={[
                      {
                        required: false,
                        message: (
                          <FormattedMessage
                            id="work.order.reply.select.language.required"
                            defaultValue="请选择目语言"
                          />
                        ),
                      },
                    ]}
                  >
                    <Select
                      defaultValue={defaultLanguage}
                      options={this.state.languageList}
                      onChange={this.handleSelect}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} style={{ textAlign: 'center' }}>
                <Col span={24}>
                  <Form.Item>
                    <Button
                      style={{ marginRight: '16px' }}
                      onClick={this.handleCancelTextTranslation}
                    >
                      <FormattedMessage
                        id="work.order.management.btn.cancel"
                        defaultMessage="取消"
                      />
                    </Button>
                    <Button type="primary" htmlType="submit">
                      <FormattedMessage
                        id="work.order.management.btn.sure"
                        defaultMessage="确定"
                      />
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </Spin>
      );
    } else {
      return (
        <Spin spinning={this.props.loadingReply}>
          <div className={styles.workReplayDetailContent}>
            <WhatsAppDetail
              queryChatAnswer={this.queryChatAnswer}
              contentList={newContentList}
              showTextTranslation={this.showTextTranslation}
              hideChatTranslationContent={this.hideChatTranslationContent}
              downLoadFile={this.downLoadFile}
              maskSensitiveInfo={this.maskSensitiveInfo}
            />
          </div>
          <Modal
            title={getIntl().formatMessage({
              id: 'work.order.reply.test.translate',
              defaultValue: '文本翻译',
            })}
            open={isModalOpen}
            onCancel={this.handleCancelTextTranslation}
            footer={null}
            className="selectLanguageModal"
          >
            <Form
              name="basic"
              autoComplete="off"
              labelAlign="right"
              ref={this.formLanguageRef}
              onFinish={this.onFinishTextTranslation}
            >
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'work.order.reply.select.language',
                      defaultValue: '选择目标语言：',
                    })}
                    name="language"
                    rules={[
                      {
                        required: false,
                        message: (
                          <FormattedMessage
                            id="work.order.reply.select.language.required"
                            defaultValue="请选择目语言"
                          />
                        ),
                      },
                    ]}
                  >
                    <Select
                      defaultValue={defaultLanguage}
                      options={this.state.languageList}
                      onChange={this.handleSelect}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24} style={{ textAlign: 'center' }}>
                <Col span={24}>
                  <Form.Item>
                    <Button
                      style={{ marginRight: '16px' }}
                      onClick={this.handleCancelTextTranslation}
                    >
                      <FormattedMessage
                        id="work.order.management.btn.cancel"
                        defaultMessage="取消"
                      />
                    </Button>
                    <Button type="primary" htmlType="submit">
                      <FormattedMessage
                        id="work.order.management.btn.sure"
                        defaultMessage="确定"
                      />
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </Modal>
        </Spin>
      );
    }
  }
}

const EmailDetail = props => {
  // 回复类型（1、客服 2、客户 3、机器人 4、智能总结 5、备注  6、系统）
  return (
    <div>
      {props.contentList?.map(item => {
        if (item.reply_type === 1) {
          let fileNum = item.ticket_file?.length;
          const emailCopy = item.email_cc_to ? item.email_cc_to.split(',') : [];
          const emailBccCopy = item.email_bcc ? item.email_bcc.split(',') : [];
          return (
            <div className={styles.agentContainer}>
              <div className={styles.emailTitle}>
                <AgentUserIcon />
                {/*<span className={styles.userName}>{props.user.userName}</span>*/}
                <span className={styles.userName}>{item.email_sender}</span>
                <span className={styles.timeText}>{item.reply_time}</span>
              </div>
              <div className={styles.userInfoContainer}>
                <span>
                  <FormattedMessage
                    id="new.worktable.work.record.recipient"
                    defaultMessage="收件人："
                  ></FormattedMessage>
                </span>
                <a title={item.email_recipient ? item.email_recipient : ''}>
                  {item.email_recipient ? item.email_recipient : '--'}
                </a>
              </div>
              <div
                style={{
                  display: emailCopy.length > 0 ? 'inline-block' : 'none',
                }}
                className={styles.userInfoContainer}
                title={emailCopy}
              >
                <span>
                  <FormattedMessage
                    id="new.worktable.email.make.copy"
                    defaultMessage="抄送给："
                  ></FormattedMessage>
                </span>
                {emailCopy?.map(items => {
                  return <a>{items}</a>;
                })}
              </div>
              <div
                style={{
                  display: emailBccCopy.length > 0 ? 'inline-block' : 'none',
                }}
                className={styles.userInfoContainer}
                title={emailBccCopy}
              >
                <span>
                  <FormattedMessage
                    id="new.worktable.email.bcc.copy"
                    defaultMessage="密送给："
                  ></FormattedMessage>
                </span>
                {emailBccCopy?.map(items => {
                  return <a>{items}</a>;
                })}
              </div>

              <p className={styles.emailTheme}>{item.email_subject}</p>
              <div className={styles.emailLine}></div>
              <div className={styles.emailText}>
                <pre dangerouslySetInnerHTML={{ __html: item.content }}></pre>
              </div>
              <div
                style={{ display: fileNum > 0 ? 'inline-block' : 'none' }}
                className={styles.attachmentContent}
              >
                <p className={styles.attachmentLabel}>
                  <span className={styles.attachmentIcon}>
                    {AttachmentIcon()}
                  </span>
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.attachment.num"
                      values={{ num: fileNum }}
                    ></FormattedMessage>
                  </span>
                </p>
                <div style={{ width: '100%', float: 'left' }}>
                  {item.ticket_file &&
                    item.ticket_file.map(fileItem => {
                      return (
                        <div className={styles.attachmentItemContainer}>
                          <span>
                            <DocumentFileIcon />
                          </span>
                          <p title={fileItem.file_name}>{fileItem.file_name}</p>
                          <div className={styles.hoverContainer}>
                            <span
                              onClick={() =>
                                downLoadFile(
                                  fileItem.bucket_name,
                                  fileItem.file_path,
                                )
                              }
                            >
                              {DownloadFileIcon()}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className={styles.replyLine}></div>
            </div>
          );
        } else if (item.reply_type === 2) {
          let fileNum = item.ticket_file?.length;
          return (
            <div className={styles.customerContainer}>
              <div className={styles.emailTitle}>
                {/*<AgentUserIcon />*/}
                {/*<span className={styles.userName}>{user.userName}</span>*/}
                <span className={styles.userName}>{item.email_sender}</span>
                <a>{item.email_recipient ? item.email_recipient : '--'}</a>
                <span className={styles.timeText}>{item.reply_time}</span>
              </div>
              <p className={styles.emailTheme}>{item.email_subject}</p>
              <div className={styles.emailLine}></div>
              <div className={styles.emailText}>
                <pre dangerouslySetInnerHTML={{ __html: item.content }}></pre>
              </div>
              <div
                style={{ display: fileNum > 0 ? 'inline-block' : 'none' }}
                className={styles.attachmentContent}
              >
                <p className={styles.attachmentLabel}>
                  <span className={styles.attachmentIcon}>
                    {AttachmentIcon()}
                  </span>
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.attachment.num"
                      values={{ num: fileNum }}
                    ></FormattedMessage>
                  </span>
                </p>
                <div style={{ width: '100%', float: 'left' }}>
                  {item.ticket_file &&
                    item.ticket_file.map(fileItem => {
                      return (
                        <div className={styles.attachmentItemContainer}>
                          <span>
                            <DocumentFileIcon />
                          </span>
                          <p title={fileItem.file_name}>{fileItem.file_name}</p>
                          <div className={styles.hoverContainer}>
                            <span
                              onClick={() =>
                                downLoadFile(
                                  fileItem.bucket_name,
                                  fileItem.file_path,
                                )
                              }
                            >
                              {DownloadFileIcon()}
                            </span>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
              <div className={styles.replyLine}></div>
            </div>
          );
        } else if (item.reply_type == 4) {
          let newContent = JSON.parse(item.content);
          return (
            <div className={styles.intelligentSummaryChatContent}>
              <p className={styles.intelligentSummaryTitle}>
                <FormattedMessage
                  id="work.order.detail.intelligent.summary.content"
                  defaultMessage="内容总结"
                />
              </p>
              <div className={styles.summaryContent}>{newContent.summary}</div>
              <div className={styles.moodContent}>
                <span className={styles.labelText}>
                  <FormattedMessage
                    id="work.order.detail.customer.mood"
                    defaultMessage="客户心情"
                  />
                </span>
                <img
                  className={styles.moodIcon}
                  src={newContent.mood == 1 ? MoodGoodActiveIcon : MoodGoodIcon}
                />
                <img
                  className={styles.moodIcon}
                  src={
                    newContent.mood == 2 ? MoodNormalActiveIcon : MoodNormalIcon
                  }
                />
                <img
                  className={styles.moodIcon}
                  src={newContent.mood == 3 ? MoodBadActiveIcon : MoodBadIcon}
                />
              </div>
              <div className={styles.toDoListContent}>
                <span className={styles.titleText}>
                  <FormattedMessage
                    id="work.order.detail.to.do"
                    defaultMessage="代办事项"
                  />
                </span>
                <ul className={styles.listDetailContent}>
                  {newContent.toDoList?.map(items => {
                    return (
                      <li
                        style={{
                          color:
                            +items.waitExecuteStatus === 1 ? '#999' : '#333',
                        }}
                      >
                        {items.waitExecuteEvent}
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          );
        } else if (item.reply_type == 5) {
          return null;
        } else {
          return null;
          // <div className={styles.emailDetailContent}>
          //   <div className={styles.replyTitle}>
          //     {(() => {
          //       if (item.content != null) {
          //         return (
          //           <div>
          //             <div
          //               className={styles.customerContent}
          //               style={{
          //                 display: item.email_sender ? 'block' : 'none',
          //               }}
          //             >
          //               <img src={EmailUser} />
          //               <span>{item.email_sender}</span>
          //             </div>
          //             <div className={styles.addresseeContent}>
          //               <img src={AddressImg} />
          //               <span>
          //                 <FormattedMessage id="work.record.recipient" />：
          //                 {item.email_recipient}
          //               </span>
          //             </div>
          //             <div className={styles.timeText}>{item.reply_time}</div>
          //           </div>
          //         );
          //       }
          //     })()}
          //   </div>
          //   <div className={styles.emailDetailBody}>
          //     <p className={styles.detailText}>
          //       <pre dangerouslySetInnerHTML={{ __html: item.content }}></pre>
          //     </p>
          //     <div className={styles.attachmentContent}>
          //       {item.ticket_file &&
          //         item.ticket_file.map(fileItem => {
          //           return (
          //             <div>
          //               <span>
          //                 <img src={AttachmentImg} />
          //                 <a
          //                   onClick={() =>
          //                     props.downLoadFile(
          //                       fileItem.bucket_name,
          //                       fileItem.file_path,
          //                     )
          //                   }
          //                   // href={fileItem.file_path}
          //                 >
          //                   {fileItem.file_name}
          //                 </a>
          //               </span>
          //             </div>
          //           );
          //         })}
          //     </div>
          //   </div>
          // </div>
        }
      })}
    </div>
  );
};
const ChatDetail = props => {
  /**
   * 回复内容类型 1.文本 2.图片 3.视频 4.文档下载  contentType
   */
  return (
    <div className={styles.chatDetailContent}>
      {props.contentList?.map(item => {
        if (item.replyType == 1) {
          if (item.contentType == '2') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.agent.name"
                        defaultMessage="（客服）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentImgText}>
                      <img src={item.content} />
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '3') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.agent.name"
                        defaultMessage="（客服）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentVideoText}>
                      {/*<ReactMarkdown>{item.content}</ReactMarkdown>*/}
                      {/*<video autoPlay src={item.content}></video>*/}
                      <video controls>
                        <source src={item.content} type="video/mp4" />
                      </video>
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '4') {
            if (item.workFiled1 != null) {
              let filePath = item.recordFileList[0].filePath;
              let bucketName = item.recordFileList[0].bucketName;
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.agent.name"
                        defaultMessage="（客服）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div
                      className={styles.downLoadText}
                      // onClick={() => props.downLoadFile(bucketName, filePath)}
                    >
                      <a href={filePath}>{item.content}</a>
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.agent.name"
                        defaultMessage="（客服）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentText}>
                      <ReactMarkdown>{item.content}</ReactMarkdown>
                    </div>
                    {/*<img*/}
                    {/*  onClick={() =>*/}
                    {/*    props.showTextTranslation(*/}
                    {/*      item.content,*/}
                    {/*      item.workRecordContentId,*/}
                    {/*    )*/}
                    {/*  }*/}
                    {/*  style={{*/}
                    {/*    display: item.translationContent ? 'none' : 'block',*/}
                    {/*  }}*/}
                    {/*  className={styles.translateChatIcon}*/}
                    {/*  src={TranslateIcon}*/}
                    {/*/>*/}
                  </div>
                  {/*<div*/}
                  {/*  className={styles.translationContent}*/}
                  {/*  style={{*/}
                  {/*    display: item.translationContent ? 'block' : 'none',*/}
                  {/*  }}*/}
                  {/*>*/}
                  {/*  <div className={styles.translationTitle}>*/}
                  {/*    <span>*/}
                  {/*      <FormattedMessage*/}
                  {/*        id="work.order.reply.translated.text"*/}
                  {/*        defaultMessage="翻译文本"*/}
                  {/*      />*/}
                  {/*    </span>*/}
                  {/*    <div*/}
                  {/*      className={styles.hideText}*/}
                  {/*      onClick={() =>*/}
                  {/*        props.hideChatTranslationContent(*/}
                  {/*          item.workRecordContentId,*/}
                  {/*        )*/}
                  {/*      }*/}
                  {/*    >*/}
                  {/*      <span>*/}
                  {/*        <FormattedMessage*/}
                  {/*          id="work.order.reply.hide.content"*/}
                  {/*          defaultMessage="隐藏内容"*/}
                  {/*        />*/}
                  {/*      </span>*/}
                  {/*      <img src={HideContentIcon} />*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*  <ReactMarkdown>{item.translationContent}</ReactMarkdown>*/}
                  {/*</div>*/}
                </div>
              );
            }
          }
        } else if (item.replyType == 3) {
          if (item.contentType == '2') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentImgText}>
                      <img src={item.content} />
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '3') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentVideoText}>
                      {/*<ReactMarkdown>{item.content}</ReactMarkdown>*/}
                      {/*<video autoPlay src={item.content}></video>*/}
                      <video controls>
                        <source src={item.content} type="video/mp4" />
                      </video>
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '4') {
            if (item.workFiled1 != null) {
              let filePath = item.recordFileList[0].filePath;
              let bucketName = item.recordFileList[0].bucketName;
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div
                      className={styles.downLoadText}
                      // onClick={() => props.downLoadFile(bucketName, filePath)}
                    >
                      <a href={filePath}>{item.content}</a>
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentText}>
                      <ReactMarkdown>{item.content}</ReactMarkdown>
                    </div>
                    {/*<img*/}
                    {/*  onClick={() =>*/}
                    {/*    props.showTextTranslation(*/}
                    {/*      item.content,*/}
                    {/*      item.workRecordContentId,*/}
                    {/*    )*/}
                    {/*  }*/}
                    {/*  style={{*/}
                    {/*    display: item.translationContent ? 'none' : 'block',*/}
                    {/*  }}*/}
                    {/*  className={styles.translateChatIcon}*/}
                    {/*  src={TranslateIcon}*/}
                    {/*/>*/}
                  </div>
                  {/*<div*/}
                  {/*  className={styles.translationContent}*/}
                  {/*  style={{*/}
                  {/*    display: item.translationContent ? 'block' : 'none',*/}
                  {/*  }}*/}
                  {/*>*/}
                  {/*  <div className={styles.translationTitle}>*/}
                  {/*    <span>*/}
                  {/*      <FormattedMessage*/}
                  {/*        id="work.order.reply.translated.text"*/}
                  {/*        defaultMessage="翻译文本"*/}
                  {/*      />*/}
                  {/*    </span>*/}
                  {/*    <div*/}
                  {/*      className={styles.hideText}*/}
                  {/*      onClick={() =>*/}
                  {/*        props.hideChatTranslationContent(*/}
                  {/*          item.workRecordContentId,*/}
                  {/*        )*/}
                  {/*      }*/}
                  {/*    >*/}
                  {/*      <span>*/}
                  {/*        <FormattedMessage*/}
                  {/*          id="work.order.reply.hide.content"*/}
                  {/*          defaultMessage="隐藏内容"*/}
                  {/*        />*/}
                  {/*      </span>*/}
                  {/*      <img src={HideContentIcon} />*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*  <ReactMarkdown>{item.translationContent}</ReactMarkdown>*/}
                  {/*</div>*/}
                </div>
              );
            }
          }
        } else if (item.replyType == 2) {
          if (item.contentType == '2') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customImgText}>
                      <img src={item.content} />
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '3') {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customVideoText}>
                      <video controls>
                        <source src={item.content} type="video/mp4" />
                      </video>
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.contentType == '4') {
            if (item.workFiled1 != null) {
              let filePath = item.recordFileList[0].filePath;
              let bucketName = item.recordFileList[0].bucketName;
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div
                      className={styles.downLoadText}
                      // onClick={() => props.downLoadFile(bucketName, filePath)}
                    >
                      <a href={filePath}>{item.content}</a>
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            if (item.workFiled1 != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{JSON.parse(item.workFiled1).from}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      {JSON.parse(item.workFiled1).sendTime}
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customText}>
                      <ReactMarkdown>{item.content}</ReactMarkdown>
                    </div>
                    {/*<img*/}
                    {/*  onClick={() =>*/}
                    {/*    props.showTextTranslation(*/}
                    {/*      item.content,*/}
                    {/*      item.workRecordContentId,*/}
                    {/*    )*/}
                    {/*  }*/}
                    {/*  style={{*/}
                    {/*    display: item.translationContent ? 'none' : 'block',*/}
                    {/*  }}*/}
                    {/*  className={styles.translateChatIcon}*/}
                    {/*  src={TranslateIcon}*/}
                    {/*/>*/}
                  </div>
                  {/*<div*/}
                  {/*  className={styles.translationContent}*/}
                  {/*  style={{*/}
                  {/*    display: item.translationContent ? 'block' : 'none',*/}
                  {/*  }}*/}
                  {/*>*/}
                  {/*  <div className={styles.translationTitle}>*/}
                  {/*    <span>*/}
                  {/*      <FormattedMessage*/}
                  {/*        id="work.order.reply.translated.text"*/}
                  {/*        defaultMessage="翻译文本"*/}
                  {/*      />*/}
                  {/*    </span>*/}
                  {/*    <div*/}
                  {/*      className={styles.hideText}*/}
                  {/*      onClick={() =>*/}
                  {/*        props.hideChatTranslationContent(*/}
                  {/*          item.workRecordContentId,*/}
                  {/*        )*/}
                  {/*      }*/}
                  {/*    >*/}
                  {/*      <span>*/}
                  {/*        <FormattedMessage*/}
                  {/*          id="work.order.reply.hide.content"*/}
                  {/*          defaultMessage="隐藏内容"*/}
                  {/*        />*/}
                  {/*      </span>*/}
                  {/*      <img src={HideContentIcon} />*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*  <ReactMarkdown>{item.translationContent}</ReactMarkdown>*/}
                  {/*</div>*/}
                </div>
              );
            }
          }
        }
      })}
    </div>
  );
};

const ChatVoiceDetail = props => {
  let [displayReply, setDisplayReply] = useState(true);
  let [showLeftRight, setShowLeftRight] = useState(true);
  const showReply = contactId => {
    setDisplayReply(!displayReply);
    props.handleVoiceAnalysis(contactId);
  };
  const hideReply = () => {
    setDisplayReply(!displayReply);
  };

  const onChangeLeftRight = checked => {
    setShowLeftRight(checked);
  };
  return (
    <div>
      {props.contentList?.map(item => {
        let contactId = item.contact_id;
        if (item.reply_type === 1) {
          if (item.ticket_file && item.ticket_file.length > 0) {
            return (
              <div className={styles.phoneDetailContent}>
                <div className={styles.replyTitle}>
                  <div>
                    <div className={styles.customerContent}>
                      <img src={UserOnline} />
                      <span>{item.email_sender}</span>
                    </div>
                    <div className={styles.timeText}>{item.reply_time}</div>
                  </div>
                </div>
                <div className={styles.phoneDetailBody}>
                  {item.ticket_file &&
                    item.ticket_file?.map(items => {
                      return (
                        <div className={styles.playAudioContent}>
                          <div
                            className={styles.phoneVoiceTitle}
                            style={{
                              display:
                                props.channelTypeId == '11' ? 'block' : 'none',
                            }}
                          >
                            <img src={AppVideoOutlinedIcon} />
                            <FormattedMessage
                              id="work.order.reply.chat.voice.app"
                              defaultMessage="APP端在线视频沟通录音"
                            />
                            <span
                              style={{
                                fontSize: '12px',
                                lineHeight: '22px',
                                display:
                                  props.translateType == 'whisper'
                                    ? 'inline-flex'
                                    : 'none',
                              }}
                            >
                              <FormattedMessage
                                id="work.order.reply.left.right"
                                defaultMessage="座席和客户对话内容分开展示"
                              />
                            </span>
                            <div
                              style={{
                                display: displayReply ? 'none' : 'block',
                              }}
                              onClick={() => showReply(contactId)}
                            >
                              <FormattedMessage
                                id="work.order.reply.show.replies"
                                defaultMessage="显示回复"
                              />
                              <img
                                style={{ transform: 'rotate(180deg)' }}
                                src={DownArrowIcon}
                              />
                            </div>
                            <div
                              style={{
                                display: displayReply ? 'block' : 'none',
                              }}
                              onClick={() => hideReply()}
                            >
                              <FormattedMessage
                                id="work.order.reply.hide.replies"
                                defaultMessage="隐藏回复"
                              />
                              <img src={DownArrowIcon} />
                            </div>
                          </div>
                          <div
                            className={styles.phoneVoiceTitle}
                            style={{
                              display:
                                props.channelTypeId == '10' ? 'block' : 'none',
                            }}
                          >
                            <img src={WebVideoOutlinedIcon} />
                            <FormattedMessage
                              id="work.order.reply.chat.voice.web"
                              defaultMessage="WEB端在线视频沟通录音"
                            />
                          </div>
                          <div className={styles.newTextContent}>
                            <div className={styles.audioBody}>
                              <AudioPlayer src={items.file_path} id={123} />
                            </div>
                            <div className={styles.speechText}>
                              <span>
                                <FormattedMessage
                                  id="work.order.reply.speech.text"
                                  defaultMessage="语音转文字"
                                />
                              </span>
                              <Switch
                                checked={showLeftRight}
                                onChange={onChangeLeftRight}
                                style={{
                                  display:
                                    props.translateType == 'whisper'
                                      ? 'inline-flex'
                                      : 'none',
                                  marginLeft: '15px',
                                  marginRight: '3px',
                                  float: 'left',
                                }}
                              />
                              <span
                                style={{ fontSize: '12px', lineHeight: '22px' }}
                              >
                                <FormattedMessage
                                  id="work.order.reply.left.right"
                                  defaultMessage="座席和客户对话内容分开展示"
                                />
                              </span>
                              <div
                                style={{
                                  display: displayReply ? 'none' : 'block',
                                }}
                                onClick={() => showReply(contactId)}
                              >
                                <FormattedMessage
                                  id="work.order.reply.show.replies"
                                  defaultMessage="显示回复"
                                />
                                <img
                                  style={{ transform: 'rotate(180deg)' }}
                                  src={DownArrowIcon}
                                />
                              </div>
                              <div
                                style={{
                                  display: displayReply ? 'block' : 'none',
                                }}
                                onClick={() => hideReply()}
                              >
                                <FormattedMessage
                                  id="work.order.reply.hide.replies"
                                  defaultMessage="隐藏回复"
                                />
                                <img src={DownArrowIcon} />
                              </div>
                            </div>
                            <div
                              className={styles.voiceText}
                              style={{
                                display: displayReply ? 'block' : 'none',
                              }}
                            >
                              {showLeftRight ? (
                                <Spin spinning={props.loadingReplyText}>
                                  {props.transcriptList?.map(items => {
                                    if (items.type == 'AGENT') {
                                      return (
                                        <div className={styles.agentItem}>
                                          <div className={styles.agentTitle}>
                                            <img src={AgentIcon} />
                                            <span className={styles.agentName}>
                                              {/*<span>{items.ParticipantId}</span>*/}
                                              <FormattedMessage
                                                id="work.order.reply.agent.name1"
                                                defaultMessage="客服"
                                              />
                                            </span>
                                          </div>
                                          <div className={styles.agentContent}>
                                            <div className={styles.agentText}>
                                              {items.content}
                                            </div>
                                            {/*<img*/}
                                            {/*  onClick={() =>*/}
                                            {/*    props.showTextTranslation1(*/}
                                            {/*      items.content,*/}
                                            {/*      items.id,*/}
                                            {/*    )*/}
                                            {/*  }*/}
                                            {/*  style={{*/}
                                            {/*    display: items.translatePhoneContent*/}
                                            {/*      ? 'none'*/}
                                            {/*      : 'block',*/}
                                            {/*  }}*/}
                                            {/*  className={styles.translateChatIcon}*/}
                                            {/*  src={TranslateIcon}*/}
                                            {/*/>*/}
                                          </div>
                                          {/*<div*/}
                                          {/*  className={styles.translationContent}*/}
                                          {/*  style={{*/}
                                          {/*    display: items.translatePhoneContent*/}
                                          {/*      ? 'block'*/}
                                          {/*      : 'none',*/}
                                          {/*  }}*/}
                                          {/*>*/}
                                          {/*  <div*/}
                                          {/*    className={styles.translationTitle}*/}
                                          {/*  >*/}
                                          {/*    <span>*/}
                                          {/*      <FormattedMessage*/}
                                          {/*        id="work.order.reply.translated.text"*/}
                                          {/*        defaultMessage="翻译文本"*/}
                                          {/*      />*/}
                                          {/*    </span>*/}
                                          {/*    <div*/}
                                          {/*      className={styles.hideText}*/}
                                          {/*      onClick={() =>*/}
                                          {/*        props.hideTranslationContent(*/}
                                          {/*          items.id,*/}
                                          {/*        )*/}
                                          {/*      }*/}
                                          {/*    >*/}
                                          {/*      <span>*/}
                                          {/*        <FormattedMessage*/}
                                          {/*          id="work.order.reply.hide.content"*/}
                                          {/*          defaultMessage="隐藏内容"*/}
                                          {/*        />*/}
                                          {/*      </span>*/}
                                          {/*      <img src={HideContentIcon} />*/}
                                          {/*    </div>*/}
                                          {/*  </div>*/}
                                          {/*  {items.translatePhoneContent}*/}
                                          {/*</div>*/}
                                        </div>
                                      );
                                    } else if (items.type == 'CUSTOMER') {
                                      console.log(
                                        '------------content---------------',
                                        content,
                                      );
                                      return (
                                        <div className={styles.customItem}>
                                          <div className={styles.customTitle}>
                                            <img src={CustomIcon} />
                                            <span className={styles.customName}>
                                              {/*<span>{items.ParticipantId}</span>*/}
                                              <FormattedMessage
                                                id="work.order.reply.custom.name1"
                                                defaultMessage="客户"
                                              />
                                            </span>
                                          </div>
                                          <div className={styles.customContent}>
                                            <div className={styles.customText}>
                                              {/*{items.content}*/}
                                              {props.maskSensitiveInfo(
                                                items.content,
                                              )}
                                            </div>
                                            {/*<img*/}
                                            {/*  onClick={() =>*/}
                                            {/*    props.showTextTranslation1(*/}
                                            {/*      items.content,*/}
                                            {/*      items.id,*/}
                                            {/*    )*/}
                                            {/*  }*/}
                                            {/*  style={{*/}
                                            {/*    display: items.translatePhoneContent*/}
                                            {/*      ? 'none'*/}
                                            {/*      : 'block',*/}
                                            {/*  }}*/}
                                            {/*  className={styles.translateChatIcon}*/}
                                            {/*  src={TranslateIcon}*/}
                                            {/*/>*/}
                                          </div>
                                          {/*<div*/}
                                          {/*  className={styles.translationContent}*/}
                                          {/*  style={{*/}
                                          {/*    display: items.translatePhoneContent*/}
                                          {/*      ? 'block'*/}
                                          {/*      : 'none',*/}
                                          {/*  }}*/}
                                          {/*>*/}
                                          {/*  <div*/}
                                          {/*    className={styles.translationTitle}*/}
                                          {/*  >*/}
                                          {/*    <span>*/}
                                          {/*      <FormattedMessage*/}
                                          {/*        id="work.order.reply.translated.text"*/}
                                          {/*        defaultMessage="翻译文本"*/}
                                          {/*      />*/}
                                          {/*    </span>*/}
                                          {/*    <div*/}
                                          {/*      className={styles.hideText}*/}
                                          {/*      onClick={() =>*/}
                                          {/*        props.hideTranslationContent(*/}
                                          {/*          items.id,*/}
                                          {/*        )*/}
                                          {/*      }*/}
                                          {/*    >*/}
                                          {/*      <span>*/}
                                          {/*        <FormattedMessage*/}
                                          {/*          id="work.order.reply.hide.content"*/}
                                          {/*          defaultMessage="隐藏内容"*/}
                                          {/*        />*/}
                                          {/*      </span>*/}
                                          {/*      <img src={HideContentIcon} />*/}
                                          {/*    </div>*/}
                                          {/*  </div>*/}
                                          {/*  {items.translatePhoneContent}*/}
                                          {/*</div>*/}
                                        </div>
                                      );
                                    }
                                  })}
                                  <div
                                    style={{
                                      display:
                                        props.transcriptList &&
                                        props.transcriptList.length > 0
                                          ? 'none'
                                          : 'block',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="work.order.reply.no.data"
                                      defaultMessage="暂无数据"
                                    />
                                  </div>
                                </Spin>
                              ) : (
                                <Spin spinning={props.loadingReplyText}>
                                  {props.transcriptText?.map(items => {
                                    return (
                                      <div
                                        style={{
                                          width: '100%',
                                          float: 'left',
                                          marginBottom: '15px',
                                        }}
                                      >
                                        <div
                                          className={styles.customTextAll}
                                          style={{
                                            display: props.transcriptText
                                              ? 'block'
                                              : 'none',
                                          }}
                                        >
                                          {items}
                                        </div>
                                      </div>
                                    );
                                  })}
                                  <div
                                    style={{
                                      display:
                                        props.transcriptList &&
                                        props.transcriptList.length > 0
                                          ? 'none'
                                          : 'block',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="work.order.reply.no.data"
                                      defaultMessage="暂无数据"
                                    />
                                  </div>
                                </Spin>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            );
          } else {
            return (
              <div className={styles.phoneDetailContent}>
                <img className={styles.voiceDataIcon} src={NoDataImg} />
                <p className={styles.voiceTipsText}>
                  <FormattedMessage
                    id="work.order.voice.no.data"
                    defaultMessage="录音文件正在同步中。。。"
                  />
                </p>
                <div className={styles.synchronousBtn}>
                  <Button
                    type={'primary'}
                    onClick={() => props.syncCallRecord(contactId)}
                  >
                    <FormattedMessage
                      id="work.order.voice.synchronous.btn"
                      defaultMessage="重新同步"
                    />
                  </Button>
                </div>
              </div>
            );
          }
        }
      })}
    </div>
  );
};

const PhoneDetail = props => {
  let [displayReply, setDisplayReply] = useState(true);
  let [showLeftRight, setShowLeftRight] = useState(true);
  const showReply = contactId => {
    setDisplayReply(!displayReply);
    props.handleVoiceAnalysis(contactId);
  };
  const hideReply = () => {
    setDisplayReply(!displayReply);
  };

  const onChangeLeftRight = checked => {
    setShowLeftRight(checked);
  };

  return (
    <div>
      {props.contentList?.map(item => {
        let contactId = item.contact_id;
        if (item.reply_type === 1) {
          if (item.ticket_file && item.ticket_file.length > 0) {
            return (
              <div className={styles.phoneDetailContent}>
                <div className={styles.replyTitle}>
                  {(() => {
                    let phoneNumber = item.email_sender;
                    return (
                      <div>
                        <div className={styles.customerContent}>
                          <img src={UserOnline} />
                          <span>{item.email_sender}</span>
                        </div>
                        {/*<div className={styles.addresseeContent}>*/}
                        {/*  <img src={CustomerService} />*/}
                        {/*  <span>*/}
                        {/*    <FormattedMessage id="work.record.recipient" />：*/}
                        {/*    {JSON.parse(item.workFiled1).to}*/}
                        {/*  </span>*/}
                        {/*</div>*/}
                        {/*<Link to="/worktable">*/}
                        {/*</Link>*/}
                        <Button
                          icon={<PhoneCallIcon />}
                          type={'primary'}
                          className={styles.callbackPhoneBtn}
                          onClick={() => props.jumpToWorkTable()}
                          style={{
                            display:
                              props.roleId === '1003' &&
                              props.agentAccessChannel?.search('3') !== -1 &&
                              phoneNumber
                                ? 'block'
                                : 'none',
                          }}
                        >
                          <FormattedMessage
                            id="work.order.reply.call.back"
                            defaultMessage="回拨"
                          />
                        </Button>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                    );
                  })()}
                </div>
                <div className={styles.phoneDetailBody}>
                  {item.ticket_file &&
                    item.ticket_file?.map(items => {
                      return (
                        <div className={styles.playAudioContent}>
                          <div className={styles.phoneVoiceTitle}>
                            <img src={PhoneVoiceIcon} />
                            <FormattedMessage
                              id="work.order.reply.phone.voice"
                              defaultMessage="电话录音"
                            />
                          </div>
                          <div className={styles.newTextContent}>
                            <div className={styles.audioBody}>
                              <AudioPlayer src={items.file_path} id={123} />
                              {props.downRecordingFile === '1' && (
                                <a
                                  href={items.file_path}
                                  className={styles.exportIcon}
                                >
                                  <ExportIcon />
                                </a>
                              )}
                            </div>
                            <div className={styles.speechText}>
                              <span>
                                <FormattedMessage
                                  id="work.order.reply.speech.text"
                                  defaultMessage="语音转文字"
                                />
                              </span>
                              <Switch
                                checked={showLeftRight}
                                onChange={onChangeLeftRight}
                                style={{
                                  display:
                                    props.translateType == 'whisper'
                                      ? 'inline-flex'
                                      : 'none',
                                  marginLeft: '15px',
                                  marginRight: '3px',
                                  float: 'left',
                                }}
                              />
                              <span
                                style={{ fontSize: '12px', lineHeight: '22px' }}
                              >
                                <FormattedMessage
                                  id="work.order.reply.left.right"
                                  defaultMessage="座席和客户对话内容分开展示"
                                />
                              </span>
                              <div
                                style={{
                                  display: displayReply ? 'none' : 'block',
                                }}
                                onClick={() => showReply(contactId)}
                              >
                                <FormattedMessage
                                  id="work.order.reply.show.replies"
                                  defaultMessage="显示回复"
                                />
                                <img
                                  style={{ transform: 'rotate(180deg)' }}
                                  src={DownArrowIcon}
                                />
                              </div>
                              <div
                                style={{
                                  display: displayReply ? 'block' : 'none',
                                }}
                                onClick={() => hideReply()}
                              >
                                <FormattedMessage
                                  id="work.order.reply.hide.replies"
                                  defaultMessage="隐藏回复"
                                />
                                <img src={DownArrowIcon} />
                              </div>
                            </div>
                            <div
                              className={styles.voiceText}
                              style={{
                                display: displayReply ? 'block' : 'none',
                              }}
                            >
                              {showLeftRight ? (
                                <Spin spinning={props.loadingReplyText}>
                                  {props.transcriptList?.map(items => {
                                    if (items.type == 'AGENT') {
                                      return (
                                        <div className={styles.agentItem}>
                                          <div className={styles.agentTitle}>
                                            <img src={AgentIcon} />
                                            <span className={styles.agentName}>
                                              {/*<span>{items.type}</span>*/}
                                              <FormattedMessage
                                                id="work.order.reply.agent.name1"
                                                defaultMessage="客服"
                                              />
                                            </span>
                                          </div>
                                          <div className={styles.agentContent}>
                                            <div className={styles.agentText}>
                                              {items.content}
                                            </div>
                                            {/*<img*/}
                                            {/*  onClick={() =>*/}
                                            {/*    props.showTextTranslation1(*/}
                                            {/*      items.content,*/}
                                            {/*      items.id,*/}
                                            {/*    )*/}
                                            {/*  }*/}
                                            {/*  style={{*/}
                                            {/*    display: items.translatePhoneContent*/}
                                            {/*      ? 'none'*/}
                                            {/*      : 'block',*/}
                                            {/*  }}*/}
                                            {/*  className={styles.translateChatIcon}*/}
                                            {/*  src={TranslateIcon}*/}
                                            {/*/>*/}
                                          </div>
                                          {/*<div*/}
                                          {/*  className={styles.translationContent}*/}
                                          {/*  style={{*/}
                                          {/*    display: items.translatePhoneContent*/}
                                          {/*      ? 'block'*/}
                                          {/*      : 'none',*/}
                                          {/*  }}*/}
                                          {/*>*/}
                                          {/*  <div*/}
                                          {/*    className={styles.translationTitle}*/}
                                          {/*  >*/}
                                          {/*    <span>*/}
                                          {/*      <FormattedMessage*/}
                                          {/*        id="work.order.reply.translated.text"*/}
                                          {/*        defaultMessage="翻译文本"*/}
                                          {/*      />*/}
                                          {/*    </span>*/}
                                          {/*    <div*/}
                                          {/*      className={styles.hideText}*/}
                                          {/*      onClick={() =>*/}
                                          {/*        props.hideTranslationContent(*/}
                                          {/*          items.id,*/}
                                          {/*        )*/}
                                          {/*      }*/}
                                          {/*    >*/}
                                          {/*      <span>*/}
                                          {/*        <FormattedMessage*/}
                                          {/*          id="work.order.reply.hide.content"*/}
                                          {/*          defaultMessage="隐藏内容"*/}
                                          {/*        />*/}
                                          {/*      </span>*/}
                                          {/*      <img src={HideContentIcon} />*/}
                                          {/*    </div>*/}
                                          {/*  </div>*/}
                                          {/*  <ReactMarkdown>*/}
                                          {/*    {items.translatePhoneContent}*/}
                                          {/*  </ReactMarkdown>*/}
                                          {/*</div>*/}
                                        </div>
                                      );
                                    } else if (items.type == 'CUSTOMER') {
                                      return (
                                        <div className={styles.customItem}>
                                          <div className={styles.customTitle}>
                                            <img src={CustomIcon} />
                                            <span className={styles.customName}>
                                              {/*<span>{items.type}</span>*/}
                                              <FormattedMessage
                                                id="work.order.reply.custom.name1"
                                                defaultMessage="客户"
                                              />
                                            </span>
                                          </div>
                                          <div className={styles.customContent}>
                                            <div className={styles.customText}>
                                              {/*{items.content}*/}
                                              {props.maskSensitiveInfo(
                                                items.content,
                                              )}
                                            </div>
                                            {/*<img*/}
                                            {/*  onClick={() =>*/}
                                            {/*    props.showTextTranslation1(*/}
                                            {/*      items.content,*/}
                                            {/*      items.id,*/}
                                            {/*    )*/}
                                            {/*  }*/}
                                            {/*  style={{*/}
                                            {/*    display: items.translatePhoneContent*/}
                                            {/*      ? 'none'*/}
                                            {/*      : 'block',*/}
                                            {/*  }}*/}
                                            {/*  className={styles.translateChatIcon}*/}
                                            {/*  src={TranslateIcon}*/}
                                            {/*/>*/}
                                          </div>
                                          {/*<div*/}
                                          {/*  className={styles.translationContent}*/}
                                          {/*  style={{*/}
                                          {/*    display: items.translatePhoneContent*/}
                                          {/*      ? 'block'*/}
                                          {/*      : 'none',*/}
                                          {/*  }}*/}
                                          {/*>*/}
                                          {/*  <div*/}
                                          {/*    className={styles.translationTitle}*/}
                                          {/*  >*/}
                                          {/*    <span>*/}
                                          {/*      <FormattedMessage*/}
                                          {/*        id="work.order.reply.translated.text"*/}
                                          {/*        defaultMessage="翻译文本"*/}
                                          {/*      />*/}
                                          {/*    </span>*/}
                                          {/*    <div*/}
                                          {/*      className={styles.hideText}*/}
                                          {/*      onClick={() =>*/}
                                          {/*        props.hideTranslationContent(*/}
                                          {/*          items.id,*/}
                                          {/*        )*/}
                                          {/*      }*/}
                                          {/*    >*/}
                                          {/*      <span>*/}
                                          {/*        <FormattedMessage*/}
                                          {/*          id="work.order.reply.hide.content"*/}
                                          {/*          defaultMessage="隐藏内容"*/}
                                          {/*        />*/}
                                          {/*      </span>*/}
                                          {/*      <img src={HideContentIcon} />*/}
                                          {/*    </div>*/}
                                          {/*  </div>*/}
                                          {/*  <ReactMarkdown>*/}
                                          {/*    {items.translatePhoneContent}*/}
                                          {/*  </ReactMarkdown>*/}
                                          {/*</div>*/}
                                        </div>
                                      );
                                    }
                                  })}
                                  <div
                                    style={{
                                      display:
                                        props.transcriptList &&
                                        props.transcriptList.length > 0
                                          ? 'none'
                                          : 'block',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="work.order.reply.no.data"
                                      defaultMessage="暂无数据"
                                    />
                                  </div>
                                </Spin>
                              ) : (
                                <Spin spinning={props.loadingReplyText}>
                                  {props.transcriptText?.map(items => {
                                    return (
                                      <div
                                        style={{
                                          width: '100%',
                                          float: 'left',
                                          marginBottom: '15px',
                                        }}
                                      >
                                        <div
                                          className={styles.customTextAll}
                                          style={{
                                            display: props.transcriptText
                                              ? 'block'
                                              : 'none',
                                          }}
                                        >
                                          {items}
                                        </div>
                                      </div>
                                    );
                                  })}
                                  <div
                                    style={{
                                      display:
                                        props.transcriptList &&
                                        props.transcriptList.length > 0
                                          ? 'none'
                                          : 'block',
                                    }}
                                  >
                                    <FormattedMessage
                                      id="work.order.reply.no.data"
                                      defaultMessage="暂无数据"
                                    />
                                  </div>
                                </Spin>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            );
          } else {
            return (
              <div className={styles.phoneDetailContent}>
                <img className={styles.voiceDataIcon} src={NoDataImg} />
                <p className={styles.voiceTipsText}>
                  <FormattedMessage
                    id="work.order.voice.no.data"
                    defaultMessage="录音文件正在同步中。。。"
                  />
                </p>
                <div className={styles.synchronousBtn}>
                  <Button
                    type={'primary'}
                    onClick={() => props.syncCallRecord(contactId)}
                  >
                    <FormattedMessage
                      id="work.order.voice.synchronous.btn"
                      defaultMessage="重新同步"
                    />
                  </Button>
                </div>
              </div>
            );
          }
        }
      })}
    </div>
  );
};

const WhatsAppDetail = props => {
  let [closeStatus, setCloseStatus] = useState(true);
  let [contentList, setContentList] = useState([]);
  let [carouseNum, setCarouseNum] = useState(1);
  const carouselRef = useRef(null);

  useEffect(() => {
    let newData = props.contentList;
    // 处理数据，新增 closeStatus 和 key
    const updatedData = newData.map((item, index) => {
      if (item.reply_type === 7) {
        const contentObj = JSON.parse(item.content);
        return {
          ...item,
          content: JSON.stringify({
            ...contentObj,
            closeStatus: true, // 新增 closeStatus
            key: item.work_record_content_id, // 新增 key，可自定义规则
          }),
        };
      }
      return item;
    });
    setContentList(updatedData);
  }, [props.contentList]);
  const handleCloseReason = intentKey => {
    const updatedData = contentList.map((item, index) => {
      if (item.work_record_content_id === intentKey) {
        const contentObj = JSON.parse(item.content);
        return {
          ...item,
          content: JSON.stringify({
            ...contentObj,
            closeStatus: !contentObj.closeStatus, // 新增 closeStatus
          }),
        };
      }
      return item;
    });
    setContentList(updatedData);
  };

  const handleNext = cardList => {
    if (cardList?.length === carouseNum) {
      setCarouseNum(1);
    } else {
      setCarouseNum(carouseNum + 1);
    }
    carouselRef.current.next(); // 调用 next() 方法
  };
  const handlePrev = cardList => {
    if (carouseNum === 1) {
      setCarouseNum(cardList?.length);
    } else {
      setCarouseNum(carouseNum - 1);
    }
    carouselRef.current.prev(); // 调用 next() 方法
  };

  /**
   * 回复内容类型 1.文本 2.图片 3.视频 4.文档下载 5.音频  contentType
   */
  return (
    <div className={styles.chatDetailContent}>
      {contentList?.map(item => {
        // 回复类型（1、客服 2、客户 3、机器人 4、智能总结 5、备注  6、系统 7、进线意图/深度思考）9.智能填单
        if (item.reply_type == 1) {
          // 回复内容类型 1、文本 2、图片 3、视频 4、附件 5、音频 6.语音加文字转录 7.form表单--只有客户有
          if (item.content_type == '2') {
            if (item.content != null) {
              return (
                <>
                  {item?.data_status === '999' ? (
                    <div className={styles.withdrawText}>
                      <span style={{ color: '#999' }}>
                        <FormattedMessage
                          id="work.order.detail.withdraw.text"
                          defaultValue="您已撤回一条消息"
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.agent.name"
                            defaultMessage="（客服）"
                          />
                        </span>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                      <div className={styles.agentContent}>
                        <div className={styles.agentImgText}>
                          <img src={item.content} />
                        </div>
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 1 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              {item.reference_content}
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 2 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <Image width={50} src={item.reference_content} />
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 3 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <video controls>
                                <source
                                  src={item.reference_content}
                                  type="video/mp4"
                                />
                              </video>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 4 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <a href={item.reference_content}>
                                {item.reference_content}
                              </a>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 5 ? (
                          <div className={styles.quoteTextContainer}>
                            <div
                              className={styles.quoteTextContent}
                              style={{ minWidth: '50%' }}
                            >
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <AudioPlayer
                                src={item.reference_content}
                                id={item.reference_work_record_content_id}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </>
              );
            }
          } else if (item.content_type == '3') {
            if (item.content != null) {
              return (
                <>
                  {item?.data_status === '999' ? (
                    <div className={styles.withdrawText}>
                      <span style={{ color: '#999' }}>
                        <FormattedMessage
                          id="work.order.detail.withdraw.text"
                          defaultValue="您已撤回一条消息"
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.agent.name"
                            defaultMessage="（客服）"
                          />
                        </span>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                      <div className={styles.agentContent}>
                        <div className={styles.agentVideoText}>
                          {/*<ReactMarkdown>{item.content}</ReactMarkdown>*/}
                          {/*<video autoPlay src={item.content}></video>*/}
                          <video controls>
                            <source src={item.content} type="video/mp4" />
                          </video>
                        </div>
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 1 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              {item.reference_content}
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 2 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <Image width={50} src={item.reference_content} />
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 3 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <video controls>
                                <source
                                  src={item.reference_content}
                                  type="video/mp4"
                                />
                              </video>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 4 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <a href={item.reference_content}>
                                {item.reference_content}
                              </a>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 5 ? (
                          <div className={styles.quoteTextContainer}>
                            <div
                              className={styles.quoteTextContent}
                              style={{ minWidth: '50%' }}
                            >
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <AudioPlayer
                                src={item.reference_content}
                                id={item.reference_work_record_content_id}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </>
              );
            }
          } else if (item.content_type == '4') {
            if (item.content != null) {
              return (
                <>
                  {item?.data_status === '999' ? (
                    <div className={styles.withdrawText}>
                      <span style={{ color: '#999' }}>
                        <FormattedMessage
                          id="work.order.detail.withdraw.text"
                          defaultValue="您已撤回一条消息"
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.agent.name"
                            defaultMessage="（客服）"
                          />
                        </span>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                      <div className={styles.agentContent}>
                        {item.ticket_file &&
                          item.ticket_file.map(items => {
                            let filePath = items.file_path;
                            let bucketName = items.bucket_name;
                            return (
                              <div
                                className={styles.downLoadText}
                                // onClick={() =>
                                //   props.downLoadFile(bucketName, filePath)
                                // }
                              >
                                <a href={filePath}>{item.content}</a>
                              </div>
                            );
                          })}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 1 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              {item.reference_content}
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 2 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <Image width={50} src={item.reference_content} />
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 3 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <video controls>
                                <source
                                  src={item.reference_content}
                                  type="video/mp4"
                                />
                              </video>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 4 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <a href={item.reference_content}>
                                {item.reference_content}
                              </a>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 5 ? (
                          <div className={styles.quoteTextContainer}>
                            <div
                              className={styles.quoteTextContent}
                              style={{ minWidth: '50%' }}
                            >
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <AudioPlayer
                                src={item.reference_content}
                                id={item.reference_work_record_content_id}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </>
              );
            }
          } else if (item.content_type == '5') {
            if (item.content != null) {
              return (
                <>
                  {item?.data_status === '999' ? (
                    <div className={styles.withdrawText}>
                      <span style={{ color: '#999' }}>
                        <FormattedMessage
                          id="work.order.detail.withdraw.text"
                          defaultValue="您已撤回一条消息"
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.agent.name"
                            defaultMessage="（客服）"
                          />
                        </span>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                      <div className={styles.agentContent}>
                        <div className={styles.audioPlayContent}>
                          <AudioPlayer
                            src={item.content}
                            id={item.work_record_content_id}
                          />
                        </div>
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 1 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              {item.reference_content}
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 2 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <Image width={50} src={item.reference_content} />
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 3 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <video controls>
                                <source
                                  src={item.reference_content}
                                  type="video/mp4"
                                />
                              </video>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 4 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <a href={item.reference_content}>
                                {item.reference_content}
                              </a>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 5 ? (
                          <div className={styles.quoteTextContainer}>
                            <div
                              className={styles.quoteTextContent}
                              style={{ minWidth: '50%' }}
                            >
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <AudioPlayer
                                src={item.reference_content}
                                id={item.reference_work_record_content_id}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </>
              );
            }
          } else if (item.content_type == '6') {
            if (item.content != null) {
              let newContent = JSON.parse(item.content);
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.agent.name"
                        defaultMessage="（客服）"
                      />
                    </span>
                    <div className={styles.timeText}>{item.reply_time}</div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.audioPlayContent}>
                      <AudioPlayer
                        src={newContent.url}
                        id={item.work_record_content_id}
                      />
                    </div>
                  </div>
                  <div className={styles.transcriptionContainer}>
                    {newContent.content}
                  </div>
                </div>
              );
            }
          } else {
            if (item.content != null) {
              return (
                <>
                  {item?.data_status === '999' ? (
                    <div className={styles.withdrawText}>
                      <span style={{ color: '#999' }}>
                        <FormattedMessage
                          id="work.order.detail.withdraw.text"
                          defaultValue="您已撤回一条消息"
                        />
                      </span>
                    </div>
                  ) : (
                    <div className={styles.agentItem}>
                      <div className={styles.agentTitle}>
                        <img src={AgentIcon} />
                        <span className={styles.agentName}>
                          <span>{item.reply_person}</span>
                          <FormattedMessage
                            id="work.order.reply.agent.name"
                            defaultMessage="（客服）"
                          />
                        </span>
                        <div className={styles.timeText}>{item.reply_time}</div>
                      </div>
                      <div className={styles.agentContent}>
                        <div className={styles.agentText}>
                          <ReactMarkdown>{item.content}</ReactMarkdown>
                          <div
                            style={{
                              display: item.translate_content
                                ? 'block'
                                : 'none',
                            }}
                            className={styles.translateLine}
                          ></div>
                          <ReactMarkdown
                            style={{
                              display: item.translate_content
                                ? 'block'
                                : 'none',
                            }}
                          >
                            {item.translate_content}
                          </ReactMarkdown>
                        </div>
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 1 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              {item.reference_content}
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 2 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <Image width={50} src={item.reference_content} />
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 3 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <video controls>
                                <source
                                  src={item.reference_content}
                                  type="video/mp4"
                                />
                              </video>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 4 ? (
                          <div className={styles.quoteTextContainer}>
                            <div className={styles.quoteTextContent}>
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <a href={item.reference_content}>
                                {item.reference_content}
                              </a>
                            </div>
                          </div>
                        ) : null}
                        {item.reference_work_record_content_id &&
                        item.reference_content_type == 5 ? (
                          <div className={styles.quoteTextContainer}>
                            <div
                              className={styles.quoteTextContent}
                              style={{ minWidth: '50%' }}
                            >
                              {item.reference_reply_person}{' '}
                              {item.reference_reply_person ? ':' : null}{' '}
                              <AudioPlayer
                                src={item.reference_content}
                                id={item.reference_work_record_content_id}
                              />
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </>
              );
            }
          }
        } else if (item.reply_type == 3) {
          if (item.content_type == '2') {
            if (item.content != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentImgText}>
                      <img src={item.content} />
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '3') {
            if (item.content != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentVideoText}>
                      {/*<ReactMarkdown>{item.content}</ReactMarkdown>*/}
                      {/*<video autoPlay src={item.content}></video>*/}
                      <video controls>
                        <source src={item.content} type="video/mp4" />
                      </video>
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '4') {
            if (item.content != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    {item.ticket_file &&
                      item.ticket_file.map(items => {
                        let filePath = items.file_path;
                        let bucketName = items.bucket_name;
                        return (
                          <div
                            className={styles.downLoadText}
                            // onClick={() =>
                            //   props.downLoadFile(bucketName, filePath)
                            // }
                          >
                            <a href={filePath}>{item.content}</a>
                          </div>
                        );
                      })}
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '5') {
            if (item.content != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.audioPlayContent}>
                      <AudioPlayer
                        src={item.content}
                        id={item.workRecordContentId}
                      />
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '8') {
            let newContent = JSON.parse(item.content);
            // configType 1 手动；2 自动
            // showType 1 横；2 竖
            if (newContent.configType === 1) {
              if (newContent.showType === 1) {
                const tabItems = newContent.issues.map((issue, index) => {
                  // 为每个 issueContents 字符串创建一个段落或其他元素
                  const childrenContent = (
                    <div>
                      {issue.issueContents.map((content, contentIndex) => (
                        <p
                          title={content}
                          key={contentIndex}
                          className={styles.panelText}
                        >
                          {' '}
                          {/* 使用 styles.panelText 类名如果需要的话 */}
                          {/* 您可以根据需要添加编号，例如：`${contentIndex + 1}. ${content}` */}
                          {`${contentIndex + 1}. ${content}`}
                        </p>
                      ))}
                    </div>
                  );

                  return {
                    label: issue.issueName,
                    key: String(index + 1), // 使用索引加1作为 key，确保唯一性
                    children: childrenContent,
                  };
                });

                return (
                  <div className={styles.agentItem}>
                    <div className={styles.agentTitle}>
                      <img src={AgentIcon} />
                      <span className={styles.agentName}>
                        <span>{item.reply_person}</span>
                        <FormattedMessage
                          id="work.order.reply.robot.name"
                          defaultMessage="（机器人）"
                        />
                      </span>
                      <div className={styles.timeText}>{item.reply_time}</div>
                    </div>
                    <div className={styles.agentContent}>
                      <div className={styles.hotspotIssuesContainer}>
                        <div className={styles.headerTitle}>
                          <span className={styles.titleIcon}>
                            {HotspotIssuesIcon()}
                          </span>
                          <span>
                            <FormattedMessage
                              id="work.order.detail.content.type.hot.spot.issues"
                              defaultMessage="热点问题"
                            />
                          </span>
                        </div>
                        <Tabs defaultActiveKey="1" items={tabItems} />
                      </div>
                    </div>
                  </div>
                );
              } else {
                let issuesData = newContent.issues;
                return (
                  <div className={styles.agentItem}>
                    <div className={styles.agentTitle}>
                      <img src={AgentIcon} />
                      <span className={styles.agentName}>
                        <span>{item.reply_person}</span>
                        <FormattedMessage
                          id="work.order.reply.robot.name"
                          defaultMessage="（机器人）"
                        />
                      </span>
                      <div className={styles.timeText}>{item.reply_time}</div>
                    </div>
                    <div className={styles.agentContent}>
                      <div className={styles.hotspotIssuesContainer}>
                        <div className={styles.headerTitle}>
                          <span className={styles.titleIcon}>
                            {HotspotIssuesIcon()}
                          </span>
                          <span>
                            <FormattedMessage
                              id="work.order.detail.content.type.hot.spot.issues"
                              defaultMessage="热点问题"
                            />
                          </span>
                        </div>
                        <Collapse
                          accordion
                          expandIconPosition={'end'}
                          ghost={true}
                        >
                          {issuesData?.map((itemData, index) => {
                            return (
                              <Panel
                                header={itemData?.issueName}
                                key={index + 1}
                              >
                                {itemData?.issueContents?.map(
                                  (detailData, detailIndex) => {
                                    return (
                                      <p
                                        title={detailData}
                                        className={styles.panelText}
                                      >
                                        {detailIndex + 1}. {detailData}
                                      </p>
                                    );
                                  },
                                )}
                              </Panel>
                            );
                          })}
                        </Collapse>
                      </div>
                    </div>
                  </div>
                );
              }
            } else {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>2025-05-14</div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.hotspotIssuesContainer}>
                      <div className={styles.headerTitle}>
                        <span className={styles.titleIcon}>
                          {HotspotIssuesIcon()}
                        </span>
                        <span>
                          <FormattedMessage
                            id="work.order.detail.content.type.hot.spot.issues"
                            defaultMessage="热点问题"
                          />
                        </span>
                      </div>
                      {newContent?.issues[0]?.issueContents?.map(
                        (itemData, itemIndex) => {
                          return (
                            <p title={itemData} className={styles.panelText1}>
                              {itemIndex + 1}.{''}
                              {itemData}
                            </p>
                          );
                        },
                      )}
                    </div>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '9') {
            let newContent = JSON.parse(item.content);
            // cardLayout  1 轮播，2 列表
            if (newContent?.cardLayout === 1) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>{item.reply_time}</div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.dynamicCard}>
                      <p className={styles.botMessage}>
                        {newContent?.botMessage ? newContent?.botMessage : null}
                      </p>
                      <Carousel ref={carouselRef} effect="fade" dots={false}>
                        {newContent?.cardList?.map(itemData => {
                          return (
                            <div className={styles.carouselItem}>
                              <img src={itemData.cardImageUrl} />
                              <p
                                title={itemData?.cardTitle}
                                className={styles.topTitleText}
                              >
                                {itemData?.cardTitle
                                  ? itemData?.cardTitle
                                  : '--'}
                              </p>
                              <div className={styles.bottomContainer}>
                                <p
                                  className={styles.priceContainer}
                                  title={itemData?.cardPrice}
                                >
                                  {/*<span className={styles.unit}>￥</span>*/}
                                  {itemData?.cardPrice && (
                                    <span className={styles.num}>
                                      {itemData?.cardPrice
                                        ? itemData?.cardPrice
                                        : '0.00'}
                                    </span>
                                  )}
                                </p>
                                {itemData?.cardNumber && (
                                  <p className={styles.numText}>
                                    x{' '}
                                    {itemData?.cardNumber
                                      ? itemData?.cardNumber
                                      : 0}
                                  </p>
                                )}
                                {itemData?.cardStatus && (
                                  <p className={styles.logisticsStatus}>
                                    {itemData?.cardStatus
                                      ? itemData?.cardStatus
                                      : '--'}
                                  </p>
                                )}
                              </div>
                            </div>
                          );
                        })}
                      </Carousel>
                      <div className={styles.operationContainer}>
                        <p>
                          <span>{carouseNum}</span>
                          {''}/{''}
                          {newContent?.cardList?.length}
                        </p>
                        <span
                          onClick={() => handleNext(newContent?.cardList)}
                          className={styles.arrowCircleIcon}
                        >
                          <RightOutlined />
                        </span>
                        <span
                          onClick={() => handlePrev(newContent?.cardList)}
                          className={styles.arrowCircleIcon}
                        >
                          <LeftOutlined />
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            } else {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>{item.reply_time}</div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.dynamicCard}>
                      <p className={styles.botMessage}>
                        {newContent?.botMessage ? newContent?.botMessage : null}
                      </p>
                      {newContent?.cardList?.map(itemData => {
                        return (
                          <Card>
                            <div className={styles.leftContainer}>
                              <img src={itemData.cardImageUrl} />
                            </div>
                            <div className={styles.rightContainer}>
                              <div className={styles.topContainer}>
                                <p
                                  title={itemData?.cardTitle}
                                  className={styles.topTitleText}
                                >
                                  {itemData?.cardTitle
                                    ? itemData?.cardTitle
                                    : '--'}
                                </p>
                                <p className={styles.priceContent}>
                                  {/*<span className={styles.priceUnit}>￥</span>*/}
                                  {itemData?.cardPrice && (
                                    <span
                                      title={itemData?.cardPrice}
                                      className={styles.priceText}
                                    >
                                      {itemData?.cardPrice
                                        ? itemData?.cardPrice
                                        : '0.00'}
                                    </span>
                                  )}
                                </p>
                                {itemData?.cardNumber && (
                                  <p className={styles.numText}>
                                    x{' '}
                                    {itemData?.cardNumber
                                      ? itemData?.cardNumber
                                      : 0}
                                  </p>
                                )}
                              </div>
                              {itemData?.cardStatus && (
                                <p className={styles.logisticsStatus}>
                                  {itemData?.cardStatus
                                    ? itemData.cardStatus
                                    : '--'}
                                </p>
                              )}
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            if (item.content != null) {
              return (
                <div className={styles.agentItem}>
                  <div className={styles.agentTitle}>
                    <img src={AgentIcon} />
                    <span className={styles.agentName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.robot.name"
                        defaultMessage="（机器人）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.agentContent}>
                    <div className={styles.agentText}>
                      <ReactMarkdown>{item.content}</ReactMarkdown>
                      <div
                        style={{
                          display: item.translate_content ? 'block' : 'none',
                        }}
                        className={styles.translateLine}
                      ></div>
                      <ReactMarkdown
                        style={{
                          display: item.translate_content ? 'block' : 'none',
                        }}
                      >
                        {item.translate_content}
                      </ReactMarkdown>
                    </div>
                    {/*<img*/}
                    {/*  onClick={() =>*/}
                    {/*    props.showTextTranslation(*/}
                    {/*      item.content,*/}
                    {/*      item.workRecordContentId,*/}
                    {/*    )*/}
                    {/*  }*/}
                    {/*  style={{*/}
                    {/*    display: item.translationContent ? 'none' : 'block',*/}
                    {/*  }}*/}
                    {/*  className={styles.translateChatIcon}*/}
                    {/*  src={TranslateIcon}*/}
                    {/*/>*/}
                  </div>
                </div>
              );
            }
          }
        } else if (item.reply_type == 2) {
          if (item.content_type == '2') {
            if (item.content != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customImgText}>
                      <img src={item.content} />
                    </div>
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '3') {
            if (item.content != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customVideoText}>
                      <video controls>
                        <source src={item.content} type="video/mp4" />
                      </video>
                    </div>
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '4') {
            if (item.content != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    {item.ticket_file &&
                      item.ticket_file.map(items => {
                        let filePath = items.file_path;
                        let bucketName = items.bucket_name;
                        return (
                          <div
                            className={styles.downLoadText}
                            // onClick={() =>
                            //   props.downLoadFile(bucketName, filePath)
                            // }
                          >
                            <a href={filePath}>{item.content}</a>
                          </div>
                        );
                      })}
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '5') {
            if (item.content != null) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    {/*<audio id="audio" src={item.content} />*/}
                    {/*<button onClick={togglePlay}>{isPlaying ? 'Pause' : 'Play'}</button>*/}
                    <div className={styles.audioPlayContent}>
                      <AudioPlayer
                        src={item.content}
                        id={item.workRecordContentId}
                      />
                    </div>
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '6') {
            if (item.content != null) {
              let newContent = JSON.parse(item.content);
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.audioPlayContent}>
                      <AudioPlayer
                        src={newContent.url}
                        id={item.work_record_content_id}
                      />
                    </div>
                  </div>
                  <div className={styles.transcriptionContainer}>
                    {/*{newContent.content}*/}
                    {props.maskSensitiveInfo(newContent.content)}
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                </div>
              );
            }
          } else if (item.content_type == '7') {
            let newContent = JSON.parse(item.content);
            {
              /*form表单*/
            }
            return (
              <div className={styles.customItem}>
                <div className={styles.customTitle}>
                  <img src={CustomIcon} />
                  <span className={styles.customName}>
                    <span>{item.reply_person}</span>
                    <FormattedMessage
                      id="work.order.reply.custom.name"
                      defaultMessage="（客户）"
                    />
                  </span>
                  <div className={styles.timeText}>
                    <span>{item.reply_time}</span>
                  </div>
                </div>
                <div className={styles.customContent}>
                  <div className={styles.formContainer}>
                    {newContent?.formList?.map(itemData => {
                      if (itemData.attributeType === '1') {
                        // 单行输入框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {itemData?.value ? itemData?.value : '--'}
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '2') {
                        // 多行输入框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              <ReactMarkdown>
                                {itemData.value ? itemData.value : '--'}
                              </ReactMarkdown>
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '3') {
                        // 单选下拉框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {itemData?.value ? itemData?.value : '--'}
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '4') {
                        let newData = itemData?.value;
                        // 多选下拉框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {newData?.length > 0
                                ? newData?.map(tagItem => {
                                    return <Tag>{tagItem}</Tag>;
                                  })
                                : '--'}
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '5') {
                        // 单选框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              <Radio.Group value={itemData?.value} disabled>
                                {itemData?.attributeValues?.map(option => {
                                  return (
                                    <Radio value={option.value}>
                                      {option.name}
                                    </Radio>
                                  );
                                })}
                              </Radio.Group>
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '6') {
                        let newData = itemData?.value;
                        // 复选框
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              <Checkbox.Group disabled={true} value={newData}>
                                {itemData?.attributeValues?.map(option => {
                                  return (
                                    <Checkbox value={option.value}>
                                      {option.name}
                                    </Checkbox>
                                  );
                                })}
                              </Checkbox.Group>
                            </div>
                          </div>
                        );
                      } else if (
                        itemData.attributeType === '7' ||
                        itemData.attributeType === '8'
                      ) {
                        // 时间选择
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {itemData?.value ? itemData?.value : '--'}
                            </div>
                          </div>
                        );
                      } else if (
                        itemData.attributeType === '9' ||
                        itemData.attributeType === '10'
                      ) {
                        let newData = itemData?.value
                          ? itemData?.value?.join(' - ')
                          : '--';
                        // 时间选择范围
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {newData ? newData : '--'}
                            </div>
                          </div>
                        );
                      } else if (itemData.attributeType === '11') {
                        // 文件上传
                        return (
                          <div className={styles.formItem}>
                            <p className={styles.formTitle}>
                              {itemData?.valueName ? itemData?.valueName : '--'}
                            </p>
                            <div className={styles.formValueText}>
                              {itemData?.value?.map(fileItem => {
                                return (
                                  <div className={styles.fileListItem}>
                                    <span className={styles.documentIcon}>
                                      {DocumentIcon()}
                                    </span>
                                    <p className={styles.fileName}>
                                      {fileItem.fileName}
                                    </p>
                                    <a
                                      download
                                      href={fileItem.path}
                                      className={styles.downloadIcon}
                                    >
                                      {DownloadIcon()}
                                    </a>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                </div>
              </div>
            );
          } else if (item.content_type == '9') {
            let newContent = JSON.parse(item.content);
            // cardLayout  1 轮播，2 列表
            if (newContent?.cardLayout === 1) {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.dynamicCard}>
                      <div className={styles.carouselItem}>
                        <img src={newContent.cardImageUrl} />
                        <p
                          title={newContent?.cardTitle}
                          className={styles.topTitleText}
                        >
                          {newContent?.cardTitle ? newContent?.cardTitle : '--'}
                        </p>
                        <div className={styles.bottomContainer}>
                          <p
                            className={styles.priceContainer}
                            title={newContent?.cardPrice}
                          >
                            {/*<span className={styles.unit}>￥</span>*/}
                            {newContent?.cardPrice && (
                              <span className={styles.num}>
                                {newContent?.cardPrice
                                  ? newContent?.cardPrice
                                  : '0.00'}
                              </span>
                            )}
                          </p>
                          {newContent?.cardNumber && (
                            <p className={styles.numText}>
                              x{' '}
                              {newContent?.cardNumber
                                ? newContent?.cardNumber
                                : 0}
                            </p>
                          )}
                          {newContent?.cardStatus && (
                            <p className={styles.logisticsStatus}>
                              {newContent?.cardStatus
                                ? newContent?.cardStatus
                                : '--'}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            } else {
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.dynamicCard}>
                      <Card>
                        <div className={styles.leftContainer}>
                          <img src={newContent.cardImageUrl} />
                        </div>
                        <div className={styles.rightContainer}>
                          <div className={styles.topContainer}>
                            <p
                              title={newContent?.cardTitle}
                              className={styles.topTitleText}
                            >
                              {newContent?.cardTitle
                                ? newContent?.cardTitle
                                : '--'}
                            </p>
                            <p className={styles.priceContent}>
                              {/*<span className={styles.priceUnit}>￥</span>*/}
                              {newContent?.cardPrice && (
                                <span
                                  title={newContent?.cardPrice}
                                  className={styles.priceText}
                                >
                                  {newContent?.cardPrice
                                    ? newContent?.cardPrice
                                    : '0.00'}
                                </span>
                              )}
                            </p>
                            {newContent?.cardNumber && (
                              <p className={styles.numText}>
                                x{' '}
                                {newContent?.cardNumber
                                  ? newContent?.cardNumber
                                  : 0}
                              </p>
                            )}
                          </div>
                          {newContent?.cardStatus && (
                            <p className={styles.logisticsStatus}>
                              {newContent?.cardStatus
                                ? newContent.cardStatus
                                : '--'}
                            </p>
                          )}
                        </div>
                      </Card>
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            if (item.content != null) {
              const maskedContent = props.maskSensitiveInfo(item.content);
              const translateContent = props.maskSensitiveInfo(
                item.translate_content,
              );
              return (
                <div className={styles.customItem}>
                  <div className={styles.customTitle}>
                    <img src={CustomIcon} />
                    <span className={styles.customName}>
                      <span>{item.reply_person}</span>
                      <FormattedMessage
                        id="work.order.reply.custom.name"
                        defaultMessage="（客户）"
                      />
                    </span>
                    <div className={styles.timeText}>
                      <span>{item.reply_time}</span>
                    </div>
                  </div>
                  <div className={styles.customContent}>
                    <div className={styles.customText}>
                      {typeof maskedContent === 'string' ? (
                        <ReactMarkdown>{maskedContent}</ReactMarkdown>
                      ) : (
                        <span>{maskedContent}</span>
                      )}
                      <div
                        style={{
                          display: item.translate_content ? 'block' : 'none',
                        }}
                        className={styles.translateLine}
                      ></div>
                      {typeof maskedContent === 'string' ? (
                        <ReactMarkdown
                          style={{
                            display: item.translate_content ? 'block' : 'none',
                          }}
                        >
                          {translateContent}
                        </ReactMarkdown>
                      ) : (
                        <span
                          style={{
                            display: item.translate_content ? 'block' : 'none',
                          }}
                        >
                          {translateContent}
                        </span>
                      )}
                      {/*<ReactMarkdown*/}
                      {/*  style={{*/}
                      {/*    display: item.translate_content ? 'block' : 'none',*/}
                      {/*  }}*/}
                      {/*>*/}
                      {/*  /!*{item.translate_content}*!/*/}
                      {/*  {props.maskSensitiveInfo(item.translate_content)}*/}
                      {/*</ReactMarkdown>*/}
                    </div>
                    {/*<img*/}
                    {/*  onClick={() =>*/}
                    {/*    props.showTextTranslation(*/}
                    {/*      item.content,*/}
                    {/*      item.workRecordContentId,*/}
                    {/*    )*/}
                    {/*  }*/}
                    {/*  style={{*/}
                    {/*    display: item.translationContent ? 'none' : 'block',*/}
                    {/*  }}*/}
                    {/*  className={styles.translateChatIcon}*/}
                    {/*  src={TranslateIcon}*/}
                    {/*/>*/}
                  </div>
                  <div
                    className={styles.intentionContainer}
                    style={{
                      display:
                        item?.incoming_intent_name &&
                        item?.intelligent_agent_name
                          ? 'block'
                          : 'none',
                    }}
                  >
                    <img src={AiIntentionIcon} />
                    <span>
                      <FormattedMessage
                        id="work.order.detail.ai.intention.text"
                        values={{
                          incomingIntentName: (
                            <span>{item?.incoming_intent_name}</span>
                          ),
                          intelligentAgentName: (
                            <span>{item?.intelligent_agent_name}</span>
                          ),
                        }}
                      />
                    </span>
                  </div>
                  {/*<div*/}
                  {/*  className={styles.translationContent}*/}
                  {/*  style={{*/}
                  {/*    display: item.translationContent ? 'block' : 'none',*/}
                  {/*  }}*/}
                  {/*>*/}
                  {/*  <div className={styles.translationTitle}>*/}
                  {/*    <span>*/}
                  {/*      <FormattedMessage*/}
                  {/*        id="work.order.reply.translated.text"*/}
                  {/*        defaultMessage="翻译文本"*/}
                  {/*      />*/}
                  {/*    </span>*/}
                  {/*    <div*/}
                  {/*      className={styles.hideText}*/}
                  {/*      onClick={() =>*/}
                  {/*        props.hideChatTranslationContent(*/}
                  {/*          item.workRecordContentId,*/}
                  {/*        )*/}
                  {/*      }*/}
                  {/*    >*/}
                  {/*      <span>*/}
                  {/*        <FormattedMessage*/}
                  {/*          id="work.order.reply.hide.content"*/}
                  {/*          defaultMessage="隐藏内容"*/}
                  {/*        />*/}
                  {/*      </span>*/}
                  {/*      <img src={HideContentIcon} />*/}
                  {/*    </div>*/}
                  {/*  </div>*/}
                  {/*  <ReactMarkdown>{item.translationContent}</ReactMarkdown>*/}
                  {/*</div>*/}
                </div>
              );
            }
          }
        } else if (item.reply_type == 4) {
          let newContent = JSON.parse(item.content);
          return (
            <div className={styles.intelligentSummaryChatContent}>
              <p className={styles.intelligentSummaryTitle}>
                <FormattedMessage
                  id="work.order.detail.intelligent.summary.content"
                  defaultMessage="内容总结"
                />
              </p>
              <div className={styles.summaryContent}>{newContent.summary}</div>
              <div className={styles.moodContent}>
                <span className={styles.labelText}>
                  <FormattedMessage
                    id="work.order.detail.customer.mood"
                    defaultMessage="客户心情"
                  />
                </span>
                <img
                  className={styles.moodIcon}
                  src={newContent.mood == 1 ? MoodGoodActiveIcon : MoodGoodIcon}
                />
                <img
                  className={styles.moodIcon}
                  src={
                    newContent.mood == 2 ? MoodNormalActiveIcon : MoodNormalIcon
                  }
                />
                <img
                  className={styles.moodIcon}
                  src={newContent.mood == 3 ? MoodBadActiveIcon : MoodBadIcon}
                />
              </div>
              <div className={styles.toDoListContent}>
                <span className={styles.titleText}>
                  <FormattedMessage
                    id="work.order.detail.to.do"
                    defaultMessage="代办事项"
                  />
                </span>
                <ul className={styles.listDetailContent}>
                  {newContent.toDoList?.map(items => {
                    return (
                      <li
                        style={{
                          color:
                            +items.waitExecuteStatus === 1 ? '#999' : '#333',
                        }}
                      >
                        {items.waitExecuteEvent}
                      </li>
                    );
                  })}
                </ul>
              </div>
            </div>
          );
        } else if (item.reply_type == 5) {
          if (item.content != null) {
            return (
              <div className={styles.agentItem}>
                <div className={styles.agentTitle}>
                  <img src={AgentIcon} />
                  <span className={styles.agentName}>
                    <span>{item.reply_person}</span>
                    <FormattedMessage
                      id="work.order.reply.agent.name"
                      defaultMessage="（客服）"
                    />
                  </span>
                  <div className={styles.timeText}>{item.reply_time}</div>
                </div>
                <div className={styles.agentContent}>
                  <div className={styles.agentTextNote}>
                    <ReactMarkdown>{item.content}</ReactMarkdown>
                    <div
                      style={{
                        display: item.translate_content ? 'block' : 'none',
                      }}
                      className={styles.translateLine}
                    ></div>
                    <ReactMarkdown
                      style={{
                        display: item.translate_content ? 'block' : 'none',
                      }}
                    >
                      {item.translate_content}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            );
          }
        } else if (item.reply_type == 6) {
          return (
            <div className={styles.systemTipsContentItem}>
              <div className={styles.systemTipsContent}>{item.content}</div>
            </div>
          );
        } else if (item.reply_type == 7) {
          let newContent = JSON.parse(item.content);
          return (
            <div className={styles.entryIntentionContainer}>
              <div className={styles.topReasonContainer}>
                <b>
                  <FormattedMessage
                    id="work.order.detail.customer.entry.intention.reason"
                    defaultMessage="客户进线原因："
                  />
                </b>
                <span>{newContent.inbound_intent}</span>
              </div>
              <div className={styles.space}></div>
              <div className={styles.bottomReasonContainer}>
                <div
                  className={styles.bottomHeader}
                  onClick={() => handleCloseReason(newContent.key)}
                >
                  <img src={AiIntentionIcon} />
                  <span
                    style={{ color: newContent.closeStatus ? '#999' : '#333' }}
                  >
                    <FormattedMessage
                      id="work.order.detail.deeply.pondered"
                      defaultMessage="已深度思考"
                    />
                  </span>
                  <img
                    className={styles.arrowIcon}
                    src={newContent.closeStatus ? NewDownIcon : NewUpIcon}
                  />
                </div>
                <div
                  className={styles.detailReasonText}
                  style={{ display: newContent.closeStatus ? 'none' : 'block' }}
                >
                  <ReactMarkdown>{newContent.deep_thought}</ReactMarkdown>
                </div>
              </div>
            </div>
          );
        } else if (item.reply_type == 9) {
          let newContent = JSON.parse(item.content);
          return (
            <div className={styles.intelligentFormFillingContainer}>
              <div className={styles.titleContainer}>
                <FormattedMessage
                  id="intelligent.form.filling.title"
                  defaultMessage="智能填单"
                />
              </div>
              <div>
                {newContent.aigcTicketSmartFillList?.map(items => {
                  return (
                    <div className={styles.detailItem}>
                      {items.attrName} :{' '}
                      {items.attrValue ? items.attrValue : '--'}
                    </div>
                  );
                })}
              </div>
            </div>
          );
        }
      })}
    </div>
  );
};

// 如果是语音形式，不涉及到回复，所以如下不再进行渲染
// const PhoneDetail = props => {
//   let [displayReply, setDisplayReply] = useState(false);
//   const showReply = () => {
//     setDisplayReply(!displayReply);
//   };
//   console.log(props);
//   return (
//     <div>
//       {props.detailList?.map(item => {
//         return (
//           <div className={styles.phoneDetailContent}>
//             <div className={styles.replyTitle}>
//               <div className={styles.customerContent}>
//                 <img src={EmailUser} />
//                 <span>來電人 +****************</span>
//               </div>
//               {/*<Button onClick={() => showReply()} className={styles.replyBtn}>*/}
//               {/*  {displayReply ? '隐藏回复' : '展开回复'}*/}
//               {/*</Button>*/}
//               <div className={styles.timeText}>2023-05-02 12:23:32</div>
//             </div>
//             <div className={styles.phoneDetailBody}>
//               <div className={styles.playAudioContent}>
//                 <label>语音录音：</label>
//                 <div className={styles.audioBody}>
//                   <AudioPlayer
//                     src={
//                       'https://goclouds.my.connect.aws/get-recording?format=wav&callLegId=687cf136-e65b-4912-9f04-3424f8209d27'
//                     }
//                     id={123}
//                   />
//                 </div>
//               </div>
//             </div>
//           </div>
//         );
//       })}
//     </div>
//   );
// };

const mapStateToProps = ({
  workOrderCenter,
  layouts,
  customerInformationManagement,
}) => {
  return {
    ...workOrderCenter,
    ...layouts,
    ...customerInformationManagement,
    // inputValue:test.inputValue
  };
};
export default connect(mapStateToProps)(WorkReplayDetailContent);
